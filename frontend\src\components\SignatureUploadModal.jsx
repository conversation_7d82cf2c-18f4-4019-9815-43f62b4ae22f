import React, { useState, useRef, useCallback } from 'react'
import { X, Upload, FileImage, Check, AlertCircle, RotateCcw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { reportsApi } from '@/lib/api'
import toast from 'react-hot-toast'

const SignatureUploadModal = ({ 
  report, 
  onClose, 
  onSignatureUploaded,
  isOpen = true 
}) => {
  const [selectedFile, setSelectedFile] = useState(null)
  const [previewUrl, setPreviewUrl] = useState(null)
  const [uploading, setUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const [signatureOptions, setSignatureOptions] = useState({
    position: { x: 50, y: 90 },
    size: { width: 150, height: 60 },
    page: 1
  })
  
  const fileInputRef = useRef(null)

  // Handle file selection
  const handleFileSelect = useCallback((file) => {
    try {
      // Validate file
      reportsApi.validateSignatureFile(file)
      
      setSelectedFile(file)
      
      // Create preview URL
      const url = URL.createObjectURL(file)
      setPreviewUrl(url)
      
    } catch (error) {
      toast.error(error.message)
    }
  }, [])

  // Handle file input change
  const handleFileChange = (e) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  // Handle drag and drop
  const handleDrag = useCallback((e) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  // Clear selected file
  const clearFile = () => {
    setSelectedFile(null)
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl)
      setPreviewUrl(null)
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  // Handle signature upload
  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a signature file')
      return
    }

    setUploading(true)
    try {
      const result = await reportsApi.uploadSignature(report.id, selectedFile, signatureOptions)
      
      toast.success('Signature uploaded successfully!')
      
      // Call the callback with the result
      if (onSignatureUploaded) {
        onSignatureUploaded(result)
      }
      
      // Close the modal
      onClose()
      
    } catch (error) {
      console.error('Error uploading signature:', error)
      toast.error(error.message || 'Failed to upload signature')
    } finally {
      setUploading(false)
    }
  }

  // Handle position/size changes
  const handleOptionChange = (field, value) => {
    setSignatureOptions(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Cleanup preview URL on unmount
  React.useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl)
      }
    }
  }, [previewUrl])

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <FileImage className="h-5 w-5" />
                <span>Upload E-Signature</span>
              </CardTitle>
              <CardDescription>
                Upload a signature image for: {report?.template_name || 'Report'}
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* File Upload Area */}
          <div className="space-y-4">
            <Label>Signature File</Label>
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
                dragActive 
                  ? 'border-blue-500 bg-blue-50' 
                  : selectedFile 
                    ? 'border-green-500 bg-green-50' 
                    : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              {selectedFile ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-center space-x-2 text-green-600">
                    <Check className="h-5 w-5" />
                    <span className="font-medium">{selectedFile.name}</span>
                  </div>
                  
                  {previewUrl && (
                    <div className="flex justify-center">
                      <img 
                        src={previewUrl} 
                        alt="Signature preview" 
                        className="max-w-xs max-h-32 border rounded shadow-sm"
                      />
                    </div>
                  )}
                  
                  <Button variant="outline" size="sm" onClick={clearFile}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Choose Different File
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                  <div>
                    <p className="text-lg font-medium text-gray-900">
                      Drop your signature file here
                    </p>
                    <p className="text-sm text-gray-500">
                      or click to browse files
                    </p>
                  </div>
                  <Button 
                    variant="outline" 
                    onClick={() => fileInputRef.current?.click()}
                  >
                    Browse Files
                  </Button>
                  <p className="text-xs text-gray-400">
                    PNG, JPG, JPEG files up to 1MB
                  </p>
                </div>
              )}
            </div>
            
            <Input
              ref={fileInputRef}
              type="file"
              accept=".png,.jpg,.jpeg,image/png,image/jpeg"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>

          {/* Signature Options */}
          {selectedFile && (
            <div className="space-y-4 border-t pt-4">
              <Label>Signature Placement Options</Label>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm">Page Number</Label>
                  <Input
                    type="number"
                    min="1"
                    value={signatureOptions.page}
                    onChange={(e) => handleOptionChange('page', parseInt(e.target.value) || 1)}
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label className="text-sm">Width (px)</Label>
                  <Input
                    type="number"
                    min="50"
                    max="300"
                    value={signatureOptions.size.width}
                    onChange={(e) => handleOptionChange('size', {
                      ...signatureOptions.size,
                      width: parseInt(e.target.value) || 150
                    })}
                    className="mt-1"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm">X Position (%)</Label>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={signatureOptions.position.x}
                    onChange={(e) => handleOptionChange('position', {
                      ...signatureOptions.position,
                      x: parseInt(e.target.value) || 50
                    })}
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label className="text-sm">Y Position (%)</Label>
                  <Input
                    type="number"
                    min="0"
                    max="100"
                    value={signatureOptions.position.y}
                    onChange={(e) => handleOptionChange('position', {
                      ...signatureOptions.position,
                      y: parseInt(e.target.value) || 90
                    })}
                    className="mt-1"
                  />
                </div>
              </div>
              
              <div className="text-xs text-gray-500">
                <AlertCircle className="h-3 w-3 inline mr-1" />
                Position is relative to page dimensions (0-100%)
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={onClose} disabled={uploading}>
              Cancel
            </Button>
            
            <Button 
              onClick={handleUpload} 
              disabled={!selectedFile || uploading}
              className="min-w-[120px]"
            >
              {uploading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Signature
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SignatureUploadModal
