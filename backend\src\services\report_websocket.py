"""
WebSocket service for real-time report generation progress tracking.

This module provides WebSocket functionality for broadcasting report generation
progress to connected clients in real-time using Flask-SocketIO.
"""

import logging
from typing import Dict, Any, Optional
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from flask import request
from threading import <PERSON>
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

class ReportWebSocketService:
    """Service for managing WebSocket connections and broadcasting report progress events."""
    
    def __init__(self, socketio: SocketIO):
        self.socketio = socketio
        self.connected_clients = {}  # {client_id: user_id}
        self.user_rooms = {}  # {user_id: set of client_ids}
        self.client_lock = Lock()
        
        # Register event handlers
        self._register_handlers()
        
    def _register_handlers(self):
        """Register SocketIO event handlers."""
        
        @self.socketio.on('connect', namespace='/ws/reports')
        def handle_connect(auth=None):
            """Handle client connection to report WebSocket."""
            try:
                client_id = request.sid
                # For now, use a simple user identification
                # In a real app, you'd extract this from JWT or session
                user_id = auth.get('user_id', 'anonymous') if auth else 'anonymous'
                
                with self.client_lock:
                    self.connected_clients[client_id] = user_id
                    if user_id not in self.user_rooms:
                        self.user_rooms[user_id] = set()
                    self.user_rooms[user_id].add(client_id)
                
                # Join user-specific room
                join_room(f'user_{user_id}', namespace='/ws/reports')
                
                logger.info(f"Client {client_id} connected to report WebSocket for user {user_id}. Total clients: {len(self.connected_clients)}")
                
                # Send connection confirmation
                emit('connection_status', {
                    'status': 'connected',
                    'message': 'Successfully connected to report progress stream',
                    'user_id': user_id
                }, namespace='/ws/reports')
                
            except Exception as e:
                logger.error(f"Error handling client connection: {e}")
                emit('error', {'message': 'Connection failed'}, namespace='/ws/reports')
        
        @self.socketio.on('disconnect', namespace='/ws/reports')
        def handle_disconnect():
            """Handle client disconnection from report WebSocket."""
            try:
                client_id = request.sid
                
                with self.client_lock:
                    user_id = self.connected_clients.pop(client_id, None)
                    if user_id and user_id in self.user_rooms:
                        self.user_rooms[user_id].discard(client_id)
                        if not self.user_rooms[user_id]:
                            del self.user_rooms[user_id]
                
                # Leave user room
                if user_id:
                    leave_room(f'user_{user_id}', namespace='/ws/reports')
                
                logger.info(f"Client {client_id} disconnected from report WebSocket. Total clients: {len(self.connected_clients)}")
                
            except Exception as e:
                logger.error(f"Error handling client disconnection: {e}")
        
        @self.socketio.on('ping', namespace='/ws/reports')
        def handle_ping():
            """Handle ping from client for connection health check."""
            emit('pong', {'timestamp': self._get_current_timestamp()}, namespace='/ws/reports')
    
    def broadcast_report_progress(self, user_id: str, report_id: str, progress_data: Dict[str, Any]) -> None:
        """
        Broadcast report generation progress to a specific user.
        
        Args:
            user_id: ID of the user who should receive the progress update
            report_id: ID of the report being generated
            progress_data: Dictionary containing progress information
                          Expected format:
                          {
                              "status": "generating|complete|error",
                              "progress": 0-100,
                              "message": "Status message",
                              "step": "current_step"
                          }
        """
        try:
            room = f'user_{user_id}'
            
            # Check if user has any connected clients
            if user_id not in self.user_rooms or not self.user_rooms[user_id]:
                logger.debug(f"No clients connected for user {user_id}, skipping progress broadcast")
                return
            
            # Format the progress event
            formatted_event = {
                'report_id': report_id,
                'timestamp': self._get_current_timestamp(),
                **progress_data
            }
            
            # Broadcast to user's room
            self.socketio.emit(
                'report_progress',
                formatted_event,
                room=room,
                namespace='/ws/reports'
            )
            
            logger.debug(f"Broadcasted report progress to user {user_id}: {formatted_event['status']} - {formatted_event.get('progress', 0)}%")
            
        except Exception as e:
            logger.error(f"Error broadcasting report progress: {e}")
    
    def broadcast_report_complete(self, user_id: str, report_id: str, result_data: Dict[str, Any]) -> None:
        """
        Broadcast report generation completion to a specific user.
        
        Args:
            user_id: ID of the user who should receive the completion notification
            report_id: ID of the completed report
            result_data: Dictionary containing completion information
                        Expected format:
                        {
                            "pdf_url": "download_url_for_pdf",
                            "docx_url": "download_url_for_docx",
                            "message": "Success message"
                        }
        """
        try:
            room = f'user_{user_id}'
            
            # Check if user has any connected clients
            if user_id not in self.user_rooms or not self.user_rooms[user_id]:
                logger.debug(f"No clients connected for user {user_id}, skipping completion broadcast")
                return
            
            # Format the completion event
            formatted_event = {
                'report_id': report_id,
                'timestamp': self._get_current_timestamp(),
                'status': 'complete',
                'progress': 100,
                **result_data
            }
            
            # Broadcast to user's room
            self.socketio.emit(
                'report_complete',
                formatted_event,
                room=room,
                namespace='/ws/reports'
            )
            
            logger.info(f"Broadcasted report completion to user {user_id}: {report_id}")
            
        except Exception as e:
            logger.error(f"Error broadcasting report completion: {e}")
    
    def broadcast_report_error(self, user_id: str, report_id: str, error_message: str) -> None:
        """
        Broadcast report generation error to a specific user.
        
        Args:
            user_id: ID of the user who should receive the error notification
            report_id: ID of the failed report
            error_message: Error message to display
        """
        try:
            room = f'user_{user_id}'
            
            # Check if user has any connected clients
            if user_id not in self.user_rooms or not self.user_rooms[user_id]:
                logger.debug(f"No clients connected for user {user_id}, skipping error broadcast")
                return
            
            # Format the error event
            formatted_event = {
                'report_id': report_id,
                'timestamp': self._get_current_timestamp(),
                'status': 'error',
                'progress': 0,
                'message': error_message
            }
            
            # Broadcast to user's room
            self.socketio.emit(
                'report_error',
                formatted_event,
                room=room,
                namespace='/ws/reports'
            )
            
            logger.warning(f"Broadcasted report error to user {user_id}: {error_message}")
            
        except Exception as e:
            logger.error(f"Error broadcasting report error: {e}")
    
    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        return datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')
    
    def get_connected_users(self) -> Dict[str, int]:
        """Get count of connected clients per user."""
        with self.client_lock:
            return {user_id: len(clients) for user_id, clients in self.user_rooms.items()}

    def broadcast_signature_event(self, user_id: str, report_id: str, event_data: Dict[str, Any]):
        """
        Broadcast signature-related events to a specific user.

        Args:
            user_id: ID of the user who should receive the event
            report_id: ID of the report being signed
            event_data: Dictionary containing signature event information
                       Expected format:
                       {
                           "event": "signature_added|report_signed",
                           "message": "Event message"
                       }
        """
        try:
            room = f'user_{user_id}'

            # Check if user has any connected clients
            if user_id not in self.user_rooms or not self.user_rooms[user_id]:
                logger.debug(f"No clients connected for user {user_id}, skipping signature event broadcast")
                return

            # Format the signature event
            formatted_event = {
                'report_id': report_id,
                'timestamp': self._get_current_timestamp(),
                **event_data
            }

            # Broadcast to user's room
            self.socketio.emit(
                'signature_event',
                formatted_event,
                room=room,
                namespace='/ws/reports'
            )

            logger.debug(f"Broadcasted signature event to user {user_id}: {event_data.get('event', 'unknown')}")

        except Exception as e:
            logger.error(f"Error broadcasting signature event: {e}")


# Global instance to be initialized in main.py
report_websocket_service: Optional[ReportWebSocketService] = None


def initialize_report_websocket(socketio: SocketIO) -> ReportWebSocketService:
    """
    Initialize the global report WebSocket service.
    
    Args:
        socketio: Flask-SocketIO instance
        
    Returns:
        Initialized ReportWebSocketService instance
    """
    global report_websocket_service
    report_websocket_service = ReportWebSocketService(socketio)
    logger.info("Report WebSocket service initialized")
    return report_websocket_service


def get_report_websocket_service() -> Optional[ReportWebSocketService]:
    """
    Get the global report WebSocket service instance.
    
    Returns:
        ReportWebSocketService instance or None if not initialized
    """
    return report_websocket_service


def broadcast_report_progress(user_id: str, report_id: str, progress_data: Dict[str, Any]) -> None:
    """
    Convenience function to broadcast report progress.
    
    Args:
        user_id: ID of the user who should receive the progress update
        report_id: ID of the report being generated
        progress_data: Progress data to broadcast
    """
    service = get_report_websocket_service()
    if service:
        service.broadcast_report_progress(user_id, report_id, progress_data)
    else:
        logger.warning("Report WebSocket service not initialized, cannot broadcast progress")


def broadcast_report_complete(user_id: str, report_id: str, result_data: Dict[str, Any]) -> None:
    """
    Convenience function to broadcast report completion.
    
    Args:
        user_id: ID of the user who should receive the completion notification
        report_id: ID of the completed report
        result_data: Result data to broadcast
    """
    service = get_report_websocket_service()
    if service:
        service.broadcast_report_complete(user_id, report_id, result_data)
    else:
        logger.warning("Report WebSocket service not initialized, cannot broadcast completion")


def broadcast_report_error(user_id: str, report_id: str, error_message: str) -> None:
    """
    Convenience function to broadcast report error.
    
    Args:
        user_id: ID of the user who should receive the error notification
        report_id: ID of the failed report
        error_message: Error message to display
    """
    service = get_report_websocket_service()
    if service:
        service.broadcast_report_error(user_id, report_id, error_message)
    else:
        logger.warning("Report WebSocket service not initialized, cannot broadcast error")


def broadcast_signature_event(user_id: str, report_id: str, event_data: Dict[str, Any]) -> None:
    """
    Convenience function to broadcast signature events.

    Args:
        user_id: ID of the user who should receive the event
        report_id: ID of the report being signed
        event_data: Dictionary containing signature event information
    """
    service = get_report_websocket_service()
    if service:
        service.broadcast_signature_event(user_id, report_id, event_data)
    else:
        logger.warning("Report WebSocket service not initialized, cannot broadcast signature event")
