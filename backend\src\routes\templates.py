from flask import Blueprint, jsonify, request
import requests
import re
import os
import json
import uuid
import logging
from datetime import datetime, timezone
from werkzeug.utils import secure_filename
from docx import Document
from src.utils.errors import error_response
from src.utils.audit import audit_event
import portalocker

logger = logging.getLogger(__name__)

templates_bp = Blueprint('templates', __name__)

# Load template configuration (IDs and names) from JSON config
CONFIG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
TEMPLATES_CONFIG_PATH = os.path.join(CONFIG_DIR, 'templates.json')
try:
    with open(TEMPLATES_CONFIG_PATH, 'r', encoding='utf-8') as f:
        templates_config = json.load(f)
except Exception:
    templates_config = {}

# Template configurations (placeholders defined in code; IDs and names from config)
TEMPLATES = {
    'ta_form': {
        'name': templates_config.get('ta_form', {}).get('name', 'TA Form 1 Template'),
        'google_doc_id': templates_config.get('ta_form', {}).get('google_doc_id', ''),
        'placeholders': [
            'Branch', 'Division', 'Headquarter', 'NameofEmployee', 'Month',
            'Basic', 'GradePay', 'PFNo', 'MobileNo', 'Designation'
        ] + [f'{field}{i}' for i in range(1, 8) for field in [
            'MonthanDate', 'TrainNo', 'TimeLeft', 'TimeArrived',
            'StationFrom', 'StationTo', 'Kms', 'DayNight', 'ObjectofJourney', 'Rate'
        ]]
    },
    'joint_report': {
        'name': templates_config.get('joint_report', {}).get('name', 'Joint Report Template'),
        'google_doc_id': templates_config.get('joint_report', {}).get('google_doc_id', ''),
        'placeholders': [
            'LocoFailureOrDetention', 'Date', 'LocoNo', 'Shed', 'SchDone', 'SchDue',
            'TrainNo', 'Load', 'LPM', 'ALP', 'Section', 'Station', 'BriefHistory',
            'Investigation', 'Conclusion', 'Responsibility', 'Supervisor1', 'Supervisor2'
        ]
    }
}

# Templates directory for uploaded files
TEMPLATES_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'templates')
TEMPLATES_INDEX_PATH = os.path.join(TEMPLATES_DIR, 'templates_index.json')

def _ensure_templates_directory():
    """Ensure templates directory exists"""
    if not os.path.exists(TEMPLATES_DIR):
        os.makedirs(TEMPLATES_DIR, exist_ok=True)

def _parse_docx_placeholders(file_path):
    """Parse DOCX file and extract placeholders in double curly braces"""
    try:
        doc = Document(file_path)
        placeholders = set()
        
        # Parse paragraphs
        for paragraph in doc.paragraphs:
            text = paragraph.text
            matches = re.findall(r'\{\{([^}]+)\}\}', text)
            placeholders.update(matches)
        
        # Parse tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    text = cell.text
                    matches = re.findall(r'\{\{([^}]+)\}\}', text)
                    placeholders.update(matches)
        
        # Parse headers and footers
        for section in doc.sections:
            for header in section.header.paragraphs:
                text = header.text
                matches = re.findall(r'\{\{([^}]+)\}\}', text)
                placeholders.update(matches)
            
            for footer in section.footer.paragraphs:
                text = footer.text
                matches = re.findall(r'\{\{([^}]+)\}\}', text)
                placeholders.update(matches)
        
        return sorted(list(placeholders))
    except Exception as e:
        raise Exception(f"Failed to parse DOCX file: {str(e)}")

def _update_templates_index(template_id, filename, placeholders):
    """Update templates index with concurrency-safe locking"""
    _ensure_templates_directory()
    
    try:
        # Read existing index or create new one
        index_data = {}
        if os.path.exists(TEMPLATES_INDEX_PATH):
            with portalocker.Lock(TEMPLATES_INDEX_PATH, mode='r', flags=portalocker.LOCK_SH, timeout=5) as locked_file:
                try:
                    index_data = json.load(locked_file)
                except (json.JSONDecodeError, FileNotFoundError):
                    index_data = {}
        
        # Update index
        index_data[template_id] = {
            'id': template_id,
            'filename': filename,
            'placeholders': placeholders,
            'uploaded_at': datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')
        }
        
        # Write updated index with exclusive lock
        with portalocker.Lock(TEMPLATES_INDEX_PATH, mode='w', flags=portalocker.LOCK_EX, timeout=5) as locked_file:
            json.dump(index_data, locked_file, indent=2, ensure_ascii=False)
            
    except Exception as e:
        raise Exception(f"Failed to update templates index: {str(e)}")

@templates_bp.route('/templates/upload', methods=['POST'])
def upload_template():
    """Upload a DOCX template file and extract placeholders"""
    # Check if file was uploaded
    if 'template' not in request.files:
        return error_response(400, 'No template file provided')
    
    file = request.files['template']
    
    # Check if file was selected
    if file.filename == '':
        return error_response(400, 'No file selected')
    
    # Validate file extension
    if not file.filename.lower().endswith('.docx'):
        return error_response(400, 'Only .docx files are allowed')
    
    # Check file size (5MB limit)
    file.seek(0, 2)  # Seek to end
    file_size = file.tell()
    file.seek(0)  # Reset to beginning
    
    if file_size > 5 * 1024 * 1024:  # 5MB in bytes
        return error_response(400, 'File size exceeds 5MB limit')
    
    try:
        # Generate unique ID for the template
        template_id = str(uuid.uuid4())
        
        # Secure the filename
        original_filename = secure_filename(file.filename)
        
        # Save file with UUID as filename
        _ensure_templates_directory()
        file_path = os.path.join(TEMPLATES_DIR, f"{template_id}.docx")
        file.save(file_path)
        
        # Parse placeholders from the DOCX
        placeholders = _parse_docx_placeholders(file_path)
        
        # Update templates index
        _update_templates_index(template_id, original_filename, placeholders)
        
        # Audit log the upload
        audit_event("template_uploaded", "anonymous", {
            "template_id": template_id,
            "filename": original_filename
        })
        
        return jsonify({
            'template_id': template_id,
            'filename': original_filename,
            'placeholders': placeholders,
            'message': 'Template uploaded successfully'
        }), 201
        
    except Exception as e:
        # Clean up file if it was saved
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
        except:
            pass
        
        return error_response(500, f'Upload failed: {str(e)}')

@templates_bp.route('/templates', methods=['GET'])
def get_templates():
    """Get list of all available templates (both hardcoded and uploaded)"""
    template_list = []

    # Add hardcoded templates
    for template_id, template_data in TEMPLATES.items():
        template_list.append({
            'id': template_id,
            'name': template_data['name'],
            'google_doc_id': template_data['google_doc_id'],
            'type': 'hardcoded'
        })

    # Add uploaded templates
    try:
        if os.path.exists(TEMPLATES_INDEX_PATH):
            with portalocker.Lock(TEMPLATES_INDEX_PATH, mode='r', flags=portalocker.LOCK_SH, timeout=5) as locked_file:
                try:
                    index_data = json.load(locked_file)
                    for template_data in index_data.values():
                        template_list.append({
                            'id': template_data['id'],
                            'name': template_data['filename'],
                            'placeholders': template_data['placeholders'],
                            'uploaded_at': template_data['uploaded_at'],
                            'type': 'uploaded'
                        })
                except (json.JSONDecodeError, FileNotFoundError):
                    pass  # No uploaded templates or corrupted file
    except Exception as e:
        # Log error but don't fail the entire request
        logger.warning(f"Failed to load uploaded templates: {e}")

    return jsonify(template_list)

@templates_bp.route('/templates/<template_id>/placeholders', methods=['GET'])
def get_template_placeholders(template_id):
    """Get placeholders for a specific template (hardcoded or uploaded)"""
    # Check hardcoded templates first
    if template_id in TEMPLATES:
        template = TEMPLATES[template_id]
        return jsonify({
            'template_id': template_id,
            'name': template['name'],
            'placeholders': template['placeholders'],
            'type': 'hardcoded'
        })

    # Check uploaded templates
    try:
        if os.path.exists(TEMPLATES_INDEX_PATH):
            with portalocker.Lock(TEMPLATES_INDEX_PATH, mode='r', flags=portalocker.LOCK_SH, timeout=5) as locked_file:
                try:
                    index_data = json.load(locked_file)
                    if template_id in index_data:
                        template = index_data[template_id]
                        return jsonify({
                            'template_id': template_id,
                            'name': template['filename'],
                            'placeholders': template['placeholders'],
                            'type': 'uploaded',
                            'uploaded_at': template['uploaded_at']
                        })
                except (json.JSONDecodeError, FileNotFoundError):
                    pass
    except Exception as e:
        logger.warning(f"Failed to load uploaded template {template_id}: {e}")

    return error_response(404, 'Template not found')

@templates_bp.route('/templates/<template_id>/content', methods=['GET'])
def get_template_content(template_id):
    """Get the raw content of a Google Doc template"""
    if template_id not in TEMPLATES:
        return error_response(404, 'Template not found')
    
    template = TEMPLATES[template_id]
    google_doc_id = template['google_doc_id']
    
    try:
        # Export Google Doc as plain text
        export_url = f"https://docs.google.com/document/d/{google_doc_id}/export?format=txt"
        response = requests.get(export_url)
        
        if response.status_code == 200:
            return jsonify({
                'template_id': template_id,
                'content': response.text
            })
        else:
            # Treat as upstream failure
            return error_response(502, 'Failed to fetch template content')
    except Exception:
        # Let global error handler return generic 500 and log traceback
        raise

@templates_bp.route('/templates/uploaded', methods=['GET'])
def list_uploaded_templates():
    """List all uploaded templates with their metadata"""
    try:
        if not os.path.exists(TEMPLATES_INDEX_PATH):
            return jsonify([])
        
        with portalocker.Lock(TEMPLATES_INDEX_PATH, mode='r', flags=portalocker.LOCK_SH, timeout=5) as locked_file:
            try:
                index_data = json.load(locked_file)
                templates_list = list(index_data.values())
                return jsonify(templates_list)
            except (json.JSONDecodeError, FileNotFoundError):
                return jsonify([])
                
    except Exception as e:
        return error_response(500, f'Failed to list templates: {str(e)}')

if __name__ == "__main__":
    # Test the placeholder parser with a sample DOCX file
    print("Testing DOCX placeholder parser...")
    
    # Check if we have any existing DOCX files to test with
    test_files = []
    if os.path.exists(TEMPLATES_DIR):
        for file in os.listdir(TEMPLATES_DIR):
            if file.endswith('.docx'):
                test_files.append(os.path.join(TEMPLATES_DIR, file))
    
    if test_files:
        print(f"Found {len(test_files)} DOCX files to test:")
        for test_file in test_files:
            try:
                placeholders = _parse_docx_placeholders(test_file)
                print(f"  {os.path.basename(test_file)}: {len(placeholders)} placeholders")
                if placeholders:
                    print(f"    Placeholders: {', '.join(placeholders[:5])}{'...' if len(placeholders) > 5 else ''}")
            except Exception as e:
                print(f"  {os.path.basename(test_file)}: Error - {e}")
    else:
        print("No DOCX files found for testing. Upload a template first!")
        print("Usage: curl -F template=@path/to/sample.docx http://localhost:6000/api/templates/upload")

