from flask import Blueprint, jsonify, request, send_file, Response
import requests
import os
import json
import uuid
import base64
from datetime import datetime
import re
import logging
from docx import Document
from docx.shared import Mm
from docx.enum.section import WD_ORIENT
import tempfile
import subprocess
import io
from PIL import Image
from PyPDF2 import PdfReader, PdfWriter
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader
import portalocker
from src.utils.files import generate_uuid_filename, safe_join
from src.utils.audit import audit_event
from src.utils.reports_index import load_reports_index, save_reports_index
from src.utils.errors import error_response
from src.services.report_websocket import broadcast_report_progress, broadcast_report_complete, broadcast_report_error
from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field, field_validator

reports_bp = Blueprint('reports', __name__)
logger = logging.getLogger(__name__)

# Storage paths and persistence helpers
BASE_DIR = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
REPORTS_DIR = os.path.join(BASE_DIR, 'reports')
DOCUMENTS_DIR = os.path.join(BASE_DIR, 'documents')
TEMPLATES_DIR = os.path.join(BASE_DIR, 'data', 'templates')
os.makedirs(REPORTS_DIR, exist_ok=True)
os.makedirs(DOCUMENTS_DIR, exist_ok=True)
os.makedirs(TEMPLATES_DIR, exist_ok=True)
REPORTS_INDEX_PATH = os.path.join(REPORTS_DIR, 'reports_index.json')
TEMPLATES_INDEX_PATH = os.path.join(TEMPLATES_DIR, 'templates_index.json')

"""In-memory storage for reports (in production, use a database).
Load from disk if present via concurrency-safe helper.
"""
REPORTS_STORAGE = load_reports_index() or []

def persist_reports_storage():
    try:
        save_reports_index(REPORTS_STORAGE)
    except Exception as e:
        # Non-fatal in dev mode
        logger.warning("Failed to persist reports index: %s", e)

# Load template configuration (IDs and names) from JSON config
CONFIG_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config')
TEMPLATES_CONFIG_PATH = os.path.join(CONFIG_DIR, 'templates.json')
try:
    with open(TEMPLATES_CONFIG_PATH, 'r', encoding='utf-8') as f:
        templates_config = json.load(f)
except Exception:
    templates_config = {}

# Template configurations (placeholders defined in code; IDs and names from config)
TEMPLATES = {
    'ta_form': {
        'name': templates_config.get('ta_form', {}).get('name', 'TA Form 1 Template'),
        'google_doc_id': templates_config.get('ta_form', {}).get('google_doc_id', ''),
        'placeholders': [
            'Branch', 'Division', 'Headquarter', 'NameofEmployee', 'Month',
            'Basic', 'GradePay', 'PFNo', 'MobileNo', 'Designation'
        ] + [f'{field}{i}' for i in range(1, 8) for field in [
            'MonthanDate', 'TrainNo', 'TimeLeft', 'TimeArrived',
            'StationFrom', 'StationTo', 'Kms', 'DayNight', 'ObjectofJourney', 'Rate'
        ]]
    },
    'joint_report': {
        'name': templates_config.get('joint_report', {}).get('name', 'Joint Report Template'),
        'google_doc_id': templates_config.get('joint_report', {}).get('google_doc_id', ''),
        'placeholders': [
            'LocoFailureOrDetention', 'Date', 'LocoNo', 'Shed', 'SchDone', 'SchDue',
            'TrainNo', 'Load', 'LPM', 'ALP', 'Section', 'Station', 'BriefHistory',
            'Investigation', 'Conclusion', 'Responsibility', 'Supervisor1', 'Supervisor2'
        ]
    }
}

# ==========================
# Pydantic request models
# ==========================

class GenerateReportRequest(BaseModel):
    template_id: str = Field(min_length=1)
    form_data: Dict[str, Any] = Field(default_factory=dict)

    @field_validator('template_id')
    @classmethod
    def template_must_exist(cls, v: str) -> str:
        if v not in TEMPLATES:
            raise ValueError('Invalid template_id')
        return v


class GenerateUploadedTemplateReportRequest(BaseModel):
    template_id: str = Field(min_length=1)
    data: Dict[str, Any] = Field(default_factory=dict)


class GetReportsQuery(BaseModel):
    search: Optional[str] = ''
    template: Optional[str] = None
    template_id: Optional[str] = None  # New parameter for filtering by template_id
    limit: Optional[int] = Field(default=50, ge=1, le=1000)  # New parameter for limiting results

    @field_validator('template')
    @classmethod
    def template_filter_valid(cls, v: Optional[str]) -> Optional[str]:
        if v and v not in TEMPLATES:
            raise ValueError('Invalid template')
        return v

    @field_validator('template_id')
    @classmethod
    def validate_template_id(cls, v: Optional[str]) -> Optional[str]:
        if v and v not in TEMPLATES:
            raise ValueError('Invalid template_id')
        return v


class ReportDownloadQuery(BaseModel):
    format: str = Field(default='pdf')

    @field_validator('format')
    @classmethod
    def validate_format(cls, v: str) -> str:
        if v not in ['docx', 'pdf']:
            raise ValueError('Format must be either docx or pdf')
        return v


class SignaturePosition(BaseModel):
    x: float = Field(ge=0, le=100)
    y: float = Field(ge=0, le=100)


class SignatureSize(BaseModel):
    width: float = Field(gt=0)
    height: float = Field(gt=0)


class SignaturePayload(BaseModel):
    signature: str = Field(min_length=10)
    page: int = Field(ge=1, default=1)
    position: SignaturePosition = Field(default_factory=lambda: SignaturePosition(x=50, y=90))
    size: SignatureSize = Field(default_factory=lambda: SignatureSize(width=150, height=60))


class SignReportRequest(BaseModel):
    signatures: List[SignaturePayload] = Field(min_length=1)


class SaveDocumentRequest(BaseModel):
    content: str = ''

def get_template_content_as_docx(template_id):
    """Download Google Doc as DOCX format"""
    if template_id not in TEMPLATES:
        return None

    google_doc_id = TEMPLATES[template_id]['google_doc_id']
    export_url = f"https://docs.google.com/document/d/{google_doc_id}/export?format=docx"

    try:
        response = requests.get(export_url)
        if response.status_code == 200:
            return response.content
        return None
    except Exception:
        return None

def load_templates_index():
    """Load templates index from JSON file with concurrency-safe locking"""
    try:
        if not os.path.exists(TEMPLATES_INDEX_PATH):
            return {}

        with portalocker.Lock(TEMPLATES_INDEX_PATH, mode='r', flags=portalocker.LOCK_SH, timeout=5) as locked_file:
            try:
                return json.load(locked_file)
            except (json.JSONDecodeError, FileNotFoundError):
                return {}
    except Exception as e:
        logger.error("Failed to load templates index: %s", e)
        return {}

def load_uploaded_template(template_id):
    """Load DOCX template content from uploaded file"""
    try:
        # Load templates index
        templates_index = load_templates_index()

        if template_id not in templates_index:
            return None

        # Get template filename
        template_filename = f"{template_id}.docx"
        template_path = safe_join(TEMPLATES_DIR, template_filename)

        if not os.path.exists(template_path):
            logger.error("Template file not found: %s", template_path)
            return None

        # Read template content
        with open(template_path, 'rb') as f:
            return f.read()

    except Exception as e:
        logger.error("Failed to load template %s: %s", template_id, e)
        return None

def validate_template_placeholders(template_id, data):
    """Validate that all required placeholders are provided in data"""
    try:
        # Load templates index
        templates_index = load_templates_index()

        if template_id not in templates_index:
            return False, ["Template not found"]

        template_info = templates_index[template_id]
        required_placeholders = set(template_info.get('placeholders', []))
        provided_placeholders = set(data.keys())

        missing_placeholders = required_placeholders - provided_placeholders

        if missing_placeholders:
            return False, sorted(list(missing_placeholders))

        return True, []

    except Exception as e:
        logger.error("Failed to validate placeholders for template %s: %s", template_id, e)
        return False, ["Validation error"]

def replace_placeholders_in_uploaded_template(docx_content, data):
    """Replace placeholders in DOCX content with provided data"""
    # Save the content to a temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
        temp_file.write(docx_content)
        temp_file_path = temp_file.name

    try:
        # Open the document
        doc = Document(temp_file_path)

        # Replace placeholders in paragraphs
        for paragraph in doc.paragraphs:
            for placeholder, value in data.items():
                placeholder_pattern = f"{{{{{placeholder}}}}}"
                if placeholder_pattern in paragraph.text:
                    # Handle multiline content by replacing with properly formatted text
                    formatted_value = str(value).replace('\n', '\n') if value else ''
                    paragraph.text = paragraph.text.replace(placeholder_pattern, formatted_value)

        # Replace placeholders in tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        for placeholder, value in data.items():
                            placeholder_pattern = f"{{{{{placeholder}}}}}"
                            if placeholder_pattern in paragraph.text:
                                formatted_value = str(value).replace('\n', '\n') if value else ''
                                paragraph.text = paragraph.text.replace(placeholder_pattern, formatted_value)

        # Replace placeholders in headers and footers
        for section in doc.sections:
            # Headers
            for header_paragraph in section.header.paragraphs:
                for placeholder, value in data.items():
                    placeholder_pattern = f"{{{{{placeholder}}}}}"
                    if placeholder_pattern in header_paragraph.text:
                        formatted_value = str(value).replace('\n', '\n') if value else ''
                        header_paragraph.text = header_paragraph.text.replace(placeholder_pattern, formatted_value)

            # Footers
            for footer_paragraph in section.footer.paragraphs:
                for placeholder, value in data.items():
                    placeholder_pattern = f"{{{{{placeholder}}}}}"
                    if placeholder_pattern in footer_paragraph.text:
                        formatted_value = str(value).replace('\n', '\n') if value else ''
                        footer_paragraph.text = footer_paragraph.text.replace(placeholder_pattern, formatted_value)

        # Save the modified document
        output_path = temp_file_path.replace('.docx', '_filled.docx')
        doc.save(output_path)

        # Read the modified content
        with open(output_path, 'rb') as f:
            modified_content = f.read()

        # Clean up temporary files
        os.unlink(temp_file_path)
        os.unlink(output_path)

        return modified_content
    except Exception as e:
        # Clean up on error
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        raise e

def save_report_metadata(report_metadata):
    """Save report metadata to reports_index.json with concurrency-safe locking"""
    try:
        # Ensure reports directory exists
        os.makedirs(REPORTS_DIR, exist_ok=True)

        # Load existing reports or create new list
        reports_list = []
        if os.path.exists(REPORTS_INDEX_PATH):
            with portalocker.Lock(REPORTS_INDEX_PATH, mode='r', flags=portalocker.LOCK_SH, timeout=5) as locked_file:
                try:
                    reports_list = json.load(locked_file)
                    if not isinstance(reports_list, list):
                        reports_list = []
                except (json.JSONDecodeError, FileNotFoundError):
                    reports_list = []

        # Add new report metadata
        reports_list.append(report_metadata)

        # Write updated reports list with exclusive lock
        with portalocker.Lock(REPORTS_INDEX_PATH, mode='w', flags=portalocker.LOCK_EX, timeout=5) as locked_file:
            json.dump(reports_list, locked_file, indent=2, ensure_ascii=False)

        return True

    except Exception as e:
        logger.error("Failed to save report metadata: %s", e)
        return False

def parse_editor_content(content):
    """Parse markdown sections from editor content into structured data"""
    sections = {}
    if not content:
        return sections
    
    # Split content by markdown headers
    lines = content.split('\n')
    current_section = 'Introduction'
    current_content = []
    
    for line in lines:
        line = line.strip()
        # Check for markdown headers (# ## ### etc.)
        if line.startswith('#'):
            # Save previous section
            if current_content:
                sections[current_section] = '\n'.join(current_content).strip()
            
            # Start new section
            current_section = line.lstrip('#').strip()
            current_content = []
        else:
            if line:  # Skip empty lines at start of sections
                current_content.append(line)
    
    # Save the last section
    if current_content:
        sections[current_section] = '\n'.join(current_content).strip()
    
    # If no sections found, treat entire content as 'Content'
    if not sections and content.strip():
        sections['Content'] = content.strip()
    
    return sections

def replace_placeholders_in_docx(docx_content, form_data, template_id, report_id=None):
    """Replace placeholders in DOCX content with form data and editor content"""
    # Save the content to a temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
        temp_file.write(docx_content)
        temp_file_path = temp_file.name
    
    try:
        # Open the document
        doc = Document(temp_file_path)
        
        # Get all placeholders from the template
        template_placeholders = TEMPLATES[template_id]['placeholders'] if template_id in TEMPLATES else []
        
        # Load editor content if available
        editor_content = ''
        editor_sections = {}
        if report_id:
            try:
                doc_path = safe_join(DOCUMENTS_DIR, f'{report_id}.txt')
            except ValueError:
                doc_path = None
            if doc_path and os.path.exists(doc_path):
                try:
                    with open(doc_path, 'r', encoding='utf-8') as f:
                        editor_content = f.read()
                    
                    # Parse markdown sections from editor content
                    editor_sections = parse_editor_content(editor_content)
                except Exception as e:
                    logger.error("Failed to load editor content: %s", e)
        
        # Combine form data with editor content sections for comprehensive replacement
        all_replacements = dict(form_data)
        
        # Map editor sections to common DOCX placeholders
        if editor_sections:
            # Map common section names to standard placeholders
            section_mapping = {
                'Investigation': 'Investigation',
                'Brief History': 'BriefHistory', 
                'Conclusion': 'Conclusion',
                'Content': 'Investigation',  # Default content goes to Investigation
                'Summary': 'Conclusion',
                'Background': 'BriefHistory',
                'Analysis': 'Investigation',
                'Findings': 'Investigation',
                'Recommendations': 'Conclusion'
            }
            
            for section_name, content in editor_sections.items():
                # Direct mapping if section name matches a template placeholder
                if section_name in template_placeholders:
                    all_replacements[section_name] = content
                else:
                    # Use mapping if available
                    mapped_name = section_mapping.get(section_name)
                    if mapped_name and mapped_name in template_placeholders:
                        all_replacements[mapped_name] = content
            
            # If we have editor content but no specific sections, use it for common fields
            if editor_content and not editor_sections:
                if 'Investigation' in template_placeholders:
                    all_replacements['Investigation'] = editor_content
                elif 'Content' in template_placeholders:
                    all_replacements['Content'] = editor_content
        
        # Replace placeholders in paragraphs
        for paragraph in doc.paragraphs:
            # First replace filled placeholders (form data + editor content)
            for placeholder, value in all_replacements.items():
                if f'{{{{{placeholder}}}}}' in paragraph.text and value and str(value).strip():
                    # Handle multiline content by replacing with properly formatted text
                    formatted_value = str(value).replace('\n', '\n')
                    paragraph.text = paragraph.text.replace(f'{{{{{placeholder}}}}}', formatted_value)
            
            # Then remove any remaining empty placeholders
            for placeholder in template_placeholders:
                if f'{{{{{placeholder}}}}}' in paragraph.text:
                    paragraph.text = paragraph.text.replace(f'{{{{{placeholder}}}}}', '')
        
        # Replace placeholders in tables
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    # First replace filled placeholders (form data + editor content)
                    for placeholder, value in all_replacements.items():
                        if f'{{{{{placeholder}}}}}' in cell.text and value and str(value).strip():
                            # Handle multiline content by replacing with properly formatted text
                            formatted_value = str(value).replace('\n', '\n')
                            cell.text = cell.text.replace(f'{{{{{placeholder}}}}}', formatted_value)
                    
                    # Then remove any remaining empty placeholders
                    for placeholder in template_placeholders:
                        if f'{{{{{placeholder}}}}}' in cell.text:
                            cell.text = cell.text.replace(f'{{{{{placeholder}}}}}', '')
        
        # Enforce page orientation per template (A4)
        try:
            if template_id == 'joint_report':
                for section in doc.sections:
                    section.orientation = WD_ORIENT.PORTRAIT
                    section.page_width = Mm(210)
                    section.page_height = Mm(297)
            elif template_id == 'ta_form':
                for section in doc.sections:
                    section.orientation = WD_ORIENT.LANDSCAPE
                    section.page_width = Mm(297)
                    section.page_height = Mm(210)
        except Exception:
            # Orientation enforcement is best-effort; continue on failure
            pass

        # Save the modified document
        output_path = temp_file_path.replace('.docx', '_filled.docx')
        doc.save(output_path)
        
        # Read the modified content
        with open(output_path, 'rb') as f:
            modified_content = f.read()
        
        # Clean up temporary files
        os.unlink(temp_file_path)
        os.unlink(output_path)
        
        return modified_content
    except Exception as e:
        # Clean up on error
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        raise e

def convert_docx_to_pdf_libreoffice(docx_content):
    """Convert DOCX content to PDF using LibreOffice"""
    # Save DOCX to temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_docx:
        temp_docx.write(docx_content)
        temp_docx_path = temp_docx.name
    
    temp_dir = None
    
    try:
        # Create temporary directory for output
        temp_dir = tempfile.mkdtemp()
        
        # Find LibreOffice executable path on Windows
        libreoffice_paths = [
            r'C:\Program Files\LibreOffice\program\soffice.exe',
            r'C:\Program Files (x86)\LibreOffice\program\soffice.exe',
            'libreoffice',  # For Linux or if in PATH
            'soffice'       # Alternative command name
        ]
        
        libreoffice_exec = None
        for path in libreoffice_paths:
            if os.path.exists(path) or os.name != 'nt':
                libreoffice_exec = path
                break
        
        if not libreoffice_exec:
            raise Exception("LibreOffice not found. Please install LibreOffice.")
        
        # Convert to PDF using LibreOffice
        try:
            result = subprocess.run([
                libreoffice_exec, '--headless', '--convert-to', 'pdf',
                '--outdir', temp_dir, temp_docx_path
            ], check=True, capture_output=True, text=True)
            
            # For debugging
            logger.debug("LibreOffice conversion output: %s", result.stdout)
            
        except subprocess.CalledProcessError as e:
            logger.error("LibreOffice error: %s", e.stderr)
            raise Exception(f"PDF conversion failed: {e.stderr}")
        
        # Find the generated PDF file
        pdf_filename = os.path.basename(temp_docx_path).replace('.docx', '.pdf')
        temp_pdf_path = os.path.join(temp_dir, pdf_filename)
        
        if not os.path.exists(temp_pdf_path):
            raise Exception(f"PDF file not created at expected path: {temp_pdf_path}")
        
        # Read PDF content
        with open(temp_pdf_path, 'rb') as f:
            pdf_content = f.read()
        
        # Clean up
        os.unlink(temp_docx_path)
        os.unlink(temp_pdf_path)
        os.rmdir(temp_dir)
        
        return pdf_content
    except Exception as e:
        # Clean up on error
        if os.path.exists(temp_docx_path):
            os.unlink(temp_docx_path)
        if temp_dir and os.path.exists(temp_dir):
            import shutil
            shutil.rmtree(temp_dir, ignore_errors=True)
        logger.exception("PDF conversion error: %s", str(e))
        raise Exception(f"Failed to convert DOCX to PDF: {str(e)}")

@reports_bp.route('/api/reports/generate', methods=['POST'])
def generate_report_from_uploaded_template():
    """Generate a report from uploaded template with placeholder replacement"""
    # Generate unique report ID
    report_id = str(uuid.uuid4())
    user_id = 'anonymous'  # In a real app, extract from JWT/session

    try:
        # Validate request payload
        payload = GenerateUploadedTemplateReportRequest.model_validate(request.get_json(silent=True) or {})
        template_id = payload.template_id
        data = payload.data

        # Emit initial progress
        broadcast_report_progress(user_id, report_id, {
            'status': 'generating',
            'progress': 10,
            'message': 'Validating template...',
            'step': 'template_validation'
        })

        # Validate that template exists and get placeholders
        is_valid, missing_placeholders = validate_template_placeholders(template_id, data)
        if not is_valid:
            error_msg = 'Template not found' if "Template not found" in missing_placeholders else f'Missing placeholders: {", ".join(missing_placeholders)}'
            broadcast_report_error(user_id, report_id, error_msg)
            if "Template not found" in missing_placeholders:
                return error_response(404, 'Template not found')
            else:
                return error_response(400, f'Missing placeholders: {", ".join(missing_placeholders)}')

        # Emit progress for template loading
        broadcast_report_progress(user_id, report_id, {
            'status': 'generating',
            'progress': 25,
            'message': 'Loading template...',
            'step': 'template_loading'
        })

        # Load template content
        template_content = load_uploaded_template(template_id)
        if not template_content:
            broadcast_report_error(user_id, report_id, 'Template file not found')
            return error_response(404, 'Template file not found')

        # Emit progress for placeholder replacement
        broadcast_report_progress(user_id, report_id, {
            'status': 'generating',
            'progress': 45,
            'message': 'Filling template with data...',
            'step': 'placeholder_replacement'
        })

        # Replace placeholders in template
        filled_docx_content = replace_placeholders_in_uploaded_template(template_content, data)

        # Emit progress for PDF conversion
        broadcast_report_progress(user_id, report_id, {
            'status': 'generating',
            'progress': 70,
            'message': 'Converting to PDF...',
            'step': 'pdf_conversion'
        })

        # Convert to PDF
        pdf_content = convert_docx_to_pdf_libreoffice(filled_docx_content)

        # Emit progress for file saving
        broadcast_report_progress(user_id, report_id, {
            'status': 'generating',
            'progress': 85,
            'message': 'Saving files...',
            'step': 'file_saving'
        })

        # Generate filenames and save files
        docx_filename = generate_uuid_filename('.docx')
        pdf_filename = generate_uuid_filename('.pdf')
        docx_path = safe_join(REPORTS_DIR, docx_filename)
        pdf_path = safe_join(REPORTS_DIR, pdf_filename)

        with open(docx_path, 'wb') as f:
            f.write(filled_docx_content)

        with open(pdf_path, 'wb') as f:
            f.write(pdf_content)

        # Create report metadata
        report_metadata = {
            'id': report_id,
            'template_id': template_id,
            'placeholders_filled': data,
            'docx_filename': docx_filename,
            'pdf_filename': pdf_filename,
            'created_at': datetime.now().isoformat().replace('+00:00', 'Z')
        }

        # Save metadata to reports_index.json
        if not save_report_metadata(report_metadata):
            # Clean up files if metadata save failed
            try:
                os.unlink(docx_path)
                os.unlink(pdf_path)
            except:
                pass
            broadcast_report_error(user_id, report_id, 'Failed to save report metadata')
            return error_response(500, 'Failed to save report metadata')

        # Audit logging
        try:
            audit_event(
                action='report_generated',
                user='anonymous',
                details={
                    'report_id': report_id,
                    'template_id': template_id,
                    'placeholders_count': len(data)
                }
            )
        except Exception:
            # Never fail the request due to audit logging
            pass

        # Emit completion event
        broadcast_report_complete(user_id, report_id, {
            'pdf_url': f'/api/reports/{report_id}/download/pdf',
            'docx_url': f'/api/reports/{report_id}/download/docx',
            'message': 'Report generated successfully'
        })

        return jsonify({
            'report_id': report_id,
            'message': 'Report generated successfully',
            'docx_filename': docx_filename,
            'pdf_filename': pdf_filename
        }), 201

    except ValueError as e:
        broadcast_report_error(user_id, report_id, f'Invalid request: {str(e)}')
        return error_response(400, f'Invalid request: {str(e)}')
    except Exception as e:
        error_message = str(e)
        broadcast_report_error(user_id, report_id, error_message)
        logger.exception("Report generation failed: %s", error_message)
        return error_response(500, f'Report generation failed: {error_message}')


@reports_bp.route('/reports/generate', methods=['POST'])
def generate_report():
    """Generate a report from template and form data"""
    payload = GenerateReportRequest.model_validate(request.get_json(silent=True) or {})
    template_id = payload.template_id
    form_data = payload.form_data

    # Generate unique report ID
    report_id = str(uuid.uuid4())
    user_id = 'anonymous'  # In a real app, extract from JWT/session

    try:
        # Emit initial progress
        broadcast_report_progress(user_id, report_id, {
            'status': 'generating',
            'progress': 10,
            'message': 'Loading template...',
            'step': 'template_loading'
        })

        # Get template content as DOCX
        docx_content = get_template_content_as_docx(template_id)
        if not docx_content:
            broadcast_report_error(user_id, report_id, 'Template content unavailable')
            return error_response(502, 'Template content unavailable')

        # Emit progress for placeholder replacement
        broadcast_report_progress(user_id, report_id, {
            'status': 'generating',
            'progress': 30,
            'message': 'Filling template with data...',
            'step': 'placeholder_replacement'
        })

        # Replace placeholders with form data and editor content
        filled_docx_content = replace_placeholders_in_docx(docx_content, form_data, template_id, report_id)

        # Emit progress for PDF conversion
        broadcast_report_progress(user_id, report_id, {
            'status': 'generating',
            'progress': 60,
            'message': 'Converting to PDF...',
            'step': 'pdf_conversion'
        })

        # Convert to PDF
        pdf_content = convert_docx_to_pdf_libreoffice(filled_docx_content)

        # Emit progress for file saving
        broadcast_report_progress(user_id, report_id, {
            'status': 'generating',
            'progress': 80,
            'message': 'Saving files...',
            'step': 'file_saving'
        })

        # Save files with opaque UUID filenames
        reports_dir = REPORTS_DIR
        docx_filename = generate_uuid_filename('.docx')
        pdf_filename = generate_uuid_filename('.pdf')
        docx_path = safe_join(reports_dir, docx_filename)
        pdf_path = safe_join(reports_dir, pdf_filename)

        with open(docx_path, 'wb') as f:
            f.write(filled_docx_content)

        with open(pdf_path, 'wb') as f:
            f.write(pdf_content)

        # Store report metadata
        report_metadata = {
            'id': report_id,
            'template_id': template_id,
            'template_name': TEMPLATES[template_id]['name'],
            'form_data': form_data,
            'docx_path': docx_path,
            'pdf_path': pdf_path,
            'created_at': datetime.now().isoformat(),
            'e_signed': False
        }

        REPORTS_STORAGE.append(report_metadata)
        persist_reports_storage()

        # Audit: report generated
        try:
            audit_event(
                action='report_generated',
                user='anonymous',
                details={'report_id': report_id, 'template_id': template_id}
            )
        except Exception:
            # Never fail the request due to audit logging
            pass

        # Emit completion event
        broadcast_report_complete(user_id, report_id, {
            'pdf_url': f'/api/reports/{report_id}/download/pdf',
            'docx_url': f'/api/reports/{report_id}/download/docx',
            'message': 'Report generated successfully'
        })

        return jsonify({
            'report_id': report_id,
            'message': 'Report generated successfully',
            'download_urls': {
                'docx': f'/api/reports/{report_id}/download/docx',
                'pdf': f'/api/reports/{report_id}/download/pdf'
            }
        })

    except Exception as e:
        # Emit error event
        error_message = str(e)
        broadcast_report_error(user_id, report_id, error_message)
        logger.error(f"Error generating report {report_id}: {error_message}")
        return error_response(500, f'Report generation failed: {error_message}')

@reports_bp.route('/reports', methods=['GET'])
def get_reports():
    """Get list of all generated reports with search and filter

    Query parameters:
    - template_id: filter by template ID
    - limit: number of results to return (default 50, max 1000)
    - search: search in template name, report ID, and form data
    - template: legacy parameter for template filtering

    Returns JSON array sorted by created_at descending
    """
    args_model = GetReportsQuery.model_validate(request.args.to_dict())
    search_query = (args_model.search or '').lower()
    template_filter = args_model.template or args_model.template_id  # Support both parameters
    limit = args_model.limit

    filtered_reports = []

    # Sort reports by created_at descending (most recent first)
    sorted_reports = sorted(REPORTS_STORAGE, key=lambda x: x.get('created_at', ''), reverse=True)

    for report in sorted_reports:
        # Apply search filter
        if search_query:
            searchable_text = f"{report.get('template_name', '')} {report['id']} {json.dumps(report.get('form_data', {}))}".lower()
            if search_query not in searchable_text:
                continue

        # Apply template filter (support both template and template_id parameters)
        if template_filter and report.get('template_id') != template_filter:
            continue

        # Create response object (exclude file paths for security)
        filtered_reports.append({
            'id': report['id'],
            'template_id': report.get('template_id'),
            'template_name': report.get('template_name'),
            'created_at': report.get('created_at'),
            'e_signed': report.get('e_signed', False),
            'download_urls': {
                'docx': f'/api/reports/{report["id"]}/download?format=docx',
                'pdf': f'/api/reports/{report["id"]}/download?format=pdf'
            }
        })

        # Apply limit
        if len(filtered_reports) >= limit:
            break

    return jsonify(filtered_reports)


@reports_bp.route('/reports/<report_id>', methods=['GET'])
def get_report(report_id):
    """Get metadata for a single report

    Returns 404 if report not found
    """
    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break

    if not report:
        return error_response(404, 'Report not found')

    # Return report metadata (exclude file paths for security)
    return jsonify({
        'id': report['id'],
        'template_id': report.get('template_id'),
        'template_name': report.get('template_name'),
        'created_at': report.get('created_at'),
        'e_signed': report.get('e_signed', False),
        'form_data': report.get('form_data', {}),
        'download_urls': {
            'docx': f'/api/reports/{report["id"]}/download?format=docx',
            'pdf': f'/api/reports/{report["id"]}/download?format=pdf'
        }
    })


@reports_bp.route('/reports/<report_id>/download', methods=['GET'])
def download_report(report_id):
    """Download a report in specified format

    Query parameters:
    - format: docx|pdf (default: pdf)

    Streams the file with proper Content-Type and Content-Disposition for download.
    """
    # Validate query parameters
    args_model = ReportDownloadQuery.model_validate(request.args.to_dict())
    format = args_model.format

    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break

    if not report:
        return error_response(404, 'Report not found')

    # Get file path with security validation
    file_path = report.get(f'{format}_path')
    if not file_path:
        return error_response(404, f'{format.upper()} file not found for this report')

    # Validate file path with safe_join to prevent traversal attacks
    try:
        validated_path = safe_join(REPORTS_DIR, os.path.basename(file_path))
        # Ensure the validated path matches the stored path
        if os.path.abspath(validated_path) != os.path.abspath(file_path):
            logger.warning("Path traversal attempt detected: %s", file_path)
            return error_response(404, 'File not found')
    except ValueError:
        logger.warning("Invalid file path detected: %s", file_path)
        return error_response(404, 'File not found')

    if not os.path.exists(validated_path):
        return error_response(404, 'File not found')

    # Audit: report downloaded
    try:
        audit_event(
            action='report_downloaded',
            user='anonymous',
            details={'report_id': report_id, 'format': format}
        )
    except Exception:
        pass

    # Set proper MIME types
    mimetype = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' if format == 'docx' else 'application/pdf'

    return send_file(
        validated_path,
        as_attachment=True,
        download_name=f'report_{report_id}.{format}',
        mimetype=mimetype
    )


@reports_bp.route('/reports/<report_id>/preview', methods=['GET'])
def preview_report(report_id):
    """Preview a report as PDF inline in browser

    Always serves PDF version inline (Content-Type: application/pdf).
    If only DOCX exists, converts on-the-fly to PDF before streaming.
    Includes temp file cleanup afterward.
    """
    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break

    if not report:
        return error_response(404, 'Report not found')

    # Try to get PDF path first
    pdf_path = report.get('pdf_path')
    docx_path = report.get('docx_path')

    temp_pdf_path = None
    try:
        if pdf_path and os.path.exists(pdf_path):
            # Validate PDF path with safe_join
            try:
                validated_pdf_path = safe_join(REPORTS_DIR, os.path.basename(pdf_path))
                if os.path.abspath(validated_pdf_path) != os.path.abspath(pdf_path):
                    logger.warning("Path traversal attempt detected: %s", pdf_path)
                    return error_response(404, 'File not found')
            except ValueError:
                logger.warning("Invalid PDF path detected: %s", pdf_path)
                return error_response(404, 'File not found')

            # Audit: report previewed
            try:
                audit_event(
                    action='report_previewed',
                    user='anonymous',
                    details={'report_id': report_id, 'format': 'pdf'}
                )
            except Exception:
                pass

            # Serve existing PDF
            return send_file(
                validated_pdf_path,
                as_attachment=False,  # Inline for preview
                mimetype='application/pdf'
            )

        elif docx_path and os.path.exists(docx_path):
            # Validate DOCX path with safe_join
            try:
                validated_docx_path = safe_join(REPORTS_DIR, os.path.basename(docx_path))
                if os.path.abspath(validated_docx_path) != os.path.abspath(docx_path):
                    logger.warning("Path traversal attempt detected: %s", docx_path)
                    return error_response(404, 'File not found')
            except ValueError:
                logger.warning("Invalid DOCX path detected: %s", docx_path)
                return error_response(404, 'File not found')

            # Convert DOCX to PDF on-the-fly
            try:
                with open(validated_docx_path, 'rb') as f:
                    docx_content = f.read()

                pdf_content = convert_docx_to_pdf_libreoffice(docx_content)

                # Create temporary PDF file for serving
                temp_pdf_path = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
                temp_pdf_path.write(pdf_content)
                temp_pdf_path.close()

                # Audit: report previewed
                try:
                    audit_event(
                        action='report_previewed',
                        user='anonymous',
                        details={'report_id': report_id, 'converted_from': 'docx'}
                    )
                except Exception:
                    pass

                return send_file(
                    temp_pdf_path.name,
                    as_attachment=False,  # Inline for preview
                    mimetype='application/pdf'
                )

            except Exception as e:
                logger.exception("Failed to convert DOCX to PDF for preview: %s", e)
                return error_response(500, 'Failed to generate PDF preview')

        else:
            return error_response(404, 'No file found for this report')

    finally:
        # Clean up temporary PDF file
        if temp_pdf_path and os.path.exists(temp_pdf_path.name):
            try:
                os.unlink(temp_pdf_path.name)
            except OSError:
                logger.warning("Failed to clean up temporary PDF file: %s", temp_pdf_path.name)


@reports_bp.route('/reports/<report_id>/sign', methods=['POST'])
def sign_report(report_id):
    """Add multiple e-signatures to a report and embed them in the PDF file"""
    payload = SignReportRequest.model_validate(request.get_json(silent=True) or {})
    signatures_data = [s.model_dump() for s in payload.signatures]

    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break

    if not report:
        return error_response(404, 'Report not found')

    # Get the PDF file path
    pdf_path = report['pdf_path']
    if not os.path.exists(pdf_path):
        return error_response(404, 'PDF file not found')

    # Open existing PDF
    pdf_reader = PdfReader(pdf_path)
    pdf_writer = PdfWriter()

    # Group signatures by page number
    signatures_by_page: Dict[int, List[Dict[str, Any]]] = {}
    for sig_data in signatures_data:
        page_num = sig_data.get('page', 1) - 1  # Convert to 0-based index
        if page_num not in signatures_by_page:
            signatures_by_page[page_num] = []
        signatures_by_page[page_num].append(sig_data)

    # Process each page
    for page_idx in range(len(pdf_reader.pages)):
        page = pdf_reader.pages[page_idx]
        page_width = float(page.mediabox.width)
        page_height = float(page.mediabox.height)

        # Check if this page has signatures
        if page_idx in signatures_by_page:
            # Create overlay PDF for all signatures on this page
            signatures_overlay_buffer = io.BytesIO()
            c = canvas.Canvas(signatures_overlay_buffer, pagesize=(page_width, page_height))

            # Process each signature on this page
            for sig_data in signatures_by_page[page_idx]:
                signature_data = sig_data.get('signature')
                position = sig_data.get('position', {'x': 50, 'y': 90})
                size = sig_data.get('size', {'width': 150, 'height': 60})

                # Extract signature image from base64
                signature_img_data = signature_data.split(',', 1)[1] if ',' in signature_data else signature_data
                signature_img_bytes = base64.b64decode(signature_img_data)

                # Create signature image
                signature_img = Image.open(io.BytesIO(signature_img_bytes))
                if signature_img.mode != 'RGBA':
                    signature_img = signature_img.convert('RGBA')

                # Create buffer for this signature
                signature_buffer = io.BytesIO()
                signature_img.save(signature_buffer, format='PNG')
                signature_buffer.seek(0)

                # Calculate signature position (convert from percentage to points)
                x_pos = (position['x'] / 100.0) * page_width
                y_pos = (position['y'] / 100.0) * page_height

                # Get signature size in points
                sig_width = size['width']
                sig_height = size['height']

                # Calculate Y position from bottom (reportlab coordinates start from bottom)
                y_pos_adjusted = page_height - y_pos - sig_height / 2

                # Draw signature image on overlay
                c.drawImage(
                    ImageReader(signature_buffer),
                    x_pos - sig_width / 2,
                    y_pos_adjusted,
                    width=sig_width,
                    height=sig_height,
                    mask='auto',
                )

            c.save()

            # Overlay signatures on the page
            signatures_overlay_buffer.seek(0)
            signatures_overlay_pdf = PdfReader(signatures_overlay_buffer)
            page.merge_page(signatures_overlay_pdf.pages[0])

        # Add the page (modified or unmodified) to the new PDF
        pdf_writer.add_page(page)

    # Create a signed version of the PDF using a new opaque UUID filename
    signed_pdf_filename = generate_uuid_filename('.pdf')
    signed_pdf_path = safe_join(REPORTS_DIR, signed_pdf_filename)
    with open(signed_pdf_path, 'wb') as output_file:
        pdf_writer.write(output_file)

    # Update report metadata
    report['e_signed'] = True
    report['signatures_data'] = signatures_data
    report['signed_at'] = datetime.now().isoformat()
    report['pdf_path'] = signed_pdf_path  # Update path to signed version
    persist_reports_storage()

    # Audit: report signed
    try:
        audit_event(
            action='report_signed',
            user='anonymous',
            details={'report_id': report_id, 'num_signatures': len(signatures_data)}
        )
    except Exception:
        pass

    return jsonify({
        'message': 'Report signed successfully',
        'download_urls': {
            'docx': f'/api/reports/{report_id}/download/docx',
            'pdf': f'/api/reports/{report_id}/download/pdf'
        }
    })

@reports_bp.route('/reports/<report_id>/document', methods=['GET'])
def get_report_document(report_id):
    """Return the saved document content for a report as plain text.
    If no saved document exists yet, return 404 so the frontend can fall back to a template."""
    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break
    
    if not report:
        return error_response(404, 'Report not found')
    
    doc_path = report.get('document_txt_path')
    if doc_path and os.path.exists(doc_path):
        try:
            with open(doc_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return Response(content, mimetype='text/plain')
        except Exception:
            # Let global handler format and log
            raise
    else:
        # Not found to trigger frontend fallback template
        return error_response(404, 'Document not found')

@reports_bp.route('/reports/<report_id>/save-document', methods=['POST'])
def save_report_document(report_id):
    """Save editor content for a report to a text file and update report metadata."""
    req = SaveDocumentRequest.model_validate(request.get_json(silent=True) or {})

    # Find the report
    report = None
    for r in REPORTS_STORAGE:
        if r['id'] == report_id:
            report = r
            break
    # If report does not exist, a stub will be created below so that saving still works

    # Ensure documents directory exists
    documents_dir = DOCUMENTS_DIR

    # Save content to file using safe path join. Reject traversal attempts
    try:
        doc_path = safe_join(documents_dir, f'{report_id}.txt')
    except ValueError:
        return error_response(400, 'Invalid path')
    with open(doc_path, 'w', encoding='utf-8') as f:
        f.write(req.content if req.content is not None else '')

    # Update report metadata
    if not report:
        # If report doesn't exist, create a minimal stub so editing works even after restarts
        report = {
            'id': report_id,
            'template_id': 'joint_report',
            'template_name': TEMPLATES['joint_report']['name'],
            'form_data': {},
            'created_at': datetime.now().isoformat(),
            'e_signed': False
        }
        REPORTS_STORAGE.append(report)
    report['document_txt_path'] = doc_path
    report['document_last_saved_at'] = datetime.now().isoformat()
    persist_reports_storage()

    return jsonify({
        'message': 'Document saved successfully',
        'document_url': f'/api/reports/{report_id}/document'
    })

