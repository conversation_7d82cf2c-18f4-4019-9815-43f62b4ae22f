import { useState, useEffect } from 'react'
import {
  ChevronUp,
  ChevronDown,
  Upload,
  FileText,
  Download,
  Trash2,
  Eye,
  User,
  Clock,
  PenTool,
  CheckCircle
} from 'lucide-react'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'

// Event type icons mapping
const EVENT_ICONS = {
  template_uploaded: Upload,
  report_generated: FileText,
  report_downloaded: Download,
  report_signed: CheckCircle,
  sign: PenTool,
  download_signed: Download,
  template_deleted: Trash2,
  report_preview: Eye,
  user_created: User,
  user_updated: User,
  user_deleted: User,
}

// Event type colors for badges
const EVENT_COLORS = {
  template_uploaded: 'bg-blue-100 text-blue-800',
  report_generated: 'bg-green-100 text-green-800',
  report_downloaded: 'bg-purple-100 text-purple-800',
  report_signed: 'bg-green-100 text-green-800',
  sign: 'bg-blue-100 text-blue-800',
  download_signed: 'bg-emerald-100 text-emerald-800',
  template_deleted: 'bg-red-100 text-red-800',
  report_preview: 'bg-gray-100 text-gray-800',
  user_created: 'bg-indigo-100 text-indigo-800',
  user_updated: 'bg-yellow-100 text-yellow-800',
  user_deleted: 'bg-red-100 text-red-800',
}

const AuditTable = ({
  logs = [],
  loading = false,
  onSort,
  sortField = 'timestamp',
  sortOrder = 'desc',
  className = "",
  newEventIds = new Set()
}) => {
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768)

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const handleSort = (field) => {
    if (onSort) {
      const newOrder = sortField === field && sortOrder === 'desc' ? 'asc' : 'desc'
      onSort(field, newOrder)
    }
  }

  const formatTimestamp = (timestamp) => {
    try {
      const date = new Date(timestamp)
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    } catch (error) {
      return timestamp
    }
  }

  const formatEventType = (action) => {
    return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  const getResourceId = (details) => {
    // Extract the most relevant resource ID from details
    if (details.report_id) return details.report_id
    if (details.template_id) return details.template_id
    if (details.user_id) return `user-${details.user_id}`
    if (details.filename) return details.filename
    return '-'
  }

  const formatDetails = (details) => {
    if (!details || typeof details !== 'object') return '-'
    
    const relevantDetails = []
    if (details.filename) relevantDetails.push(`File: ${details.filename}`)
    if (details.format) relevantDetails.push(`Format: ${details.format.toUpperCase()}`)
    if (details.placeholders_count) relevantDetails.push(`Fields: ${details.placeholders_count}`)
    if (details.num_signatures) relevantDetails.push(`Signatures: ${details.num_signatures}`)
    if (details.username) relevantDetails.push(`User: ${details.username}`)
    
    return relevantDetails.length > 0 ? relevantDetails.join(', ') : '-'
  }

  const SortButton = ({ field, children }) => (
    <button
      onClick={() => handleSort(field)}
      className="flex items-center space-x-1 hover:text-blue-600 transition-colors"
    >
      <span>{children}</span>
      {sortField === field && (
        sortOrder === 'desc' ? 
          <ChevronDown className="h-4 w-4" /> : 
          <ChevronUp className="h-4 w-4" />
      )}
    </button>
  )

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-16 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  if (logs.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <Clock className="h-16 w-16 text-gray-300 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Audit Logs Found</h3>
        <p className="text-gray-600">
          No audit events match your current search criteria.
        </p>
      </div>
    )
  }

  // Mobile card view
  if (isMobile) {
    return (
      <div className={`space-y-4 ${className}`}>
        {logs.map((log, index) => {
          const IconComponent = EVENT_ICONS[log.action] || Clock
          const eventColor = EVENT_COLORS[log.action] || 'bg-gray-100 text-gray-800'

          // Create unique ID for this event
          const eventId = `${log.timestamp}-${log.action}-${log.user}`
          const isNewEvent = newEventIds.has(eventId)

          // Check if this is a signature-related event that should be clickable
          const isSignatureEvent = log.action === 'download_signed' || log.action === 'sign'
          const reportId = getResourceId(log.details)

          const handleCardClick = () => {
            if (isSignatureEvent && reportId && log.action === 'download_signed') {
              // Open signed PDF in new tab
              const signedUrl = `/api/reports/${reportId}/signed`
              window.open(signedUrl, '_blank')
            }
          }

          return (
            <Card
              key={index}
              className={`transition-all duration-300 ${
                isNewEvent
                  ? 'border-l-4 border-blue-400 bg-blue-50 animate-pulse'
                  : ''
              } ${
                isSignatureEvent && log.action === 'download_signed'
                  ? 'hover:shadow-lg cursor-pointer hover:bg-blue-50'
                  : 'hover:shadow-md'
              }`}
              onClick={handleCardClick}
            >
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <IconComponent className="h-5 w-5 text-gray-500 mt-1" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <Badge className={`text-xs ${eventColor}`}>
                        {formatEventType(log.action)}
                      </Badge>
                      <span className="text-xs text-gray-500">
                        {formatTimestamp(log.timestamp)}
                      </span>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Resource:</span>
                        <span className="ml-2 text-gray-900 font-mono text-xs">
                          {getResourceId(log.details)}
                        </span>
                      </div>
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">User:</span>
                        <span className="ml-2 text-gray-900">{log.user}</span>
                      </div>
                      {formatDetails(log.details) !== '-' && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">Details:</span>
                          <span className="ml-2 text-gray-600">{formatDetails(log.details)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    )
  }

  // Desktop table view
  return (
    <div className={`overflow-x-auto ${className}`}>
      <Table>
        <TableHeader>
          <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50">
            <TableHead className="font-semibold">
              <SortButton field="timestamp">Timestamp</SortButton>
            </TableHead>
            <TableHead className="font-semibold">
              <SortButton field="action">Event Type</SortButton>
            </TableHead>
            <TableHead className="font-semibold">Resource ID</TableHead>
            <TableHead className="font-semibold">User</TableHead>
            <TableHead className="font-semibold">Details</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {logs.map((log, index) => {
            const IconComponent = EVENT_ICONS[log.action] || Clock
            const eventColor = EVENT_COLORS[log.action] || 'bg-gray-100 text-gray-800'

            // Create unique ID for this event
            const eventId = `${log.timestamp}-${log.action}-${log.user}`
            const isNewEvent = newEventIds.has(eventId)

            // Check if this is a signature-related event that should be clickable
            const isSignatureEvent = log.action === 'download_signed' || log.action === 'sign'
            const reportId = getResourceId(log.details)

            const handleRowClick = () => {
              if (isSignatureEvent && reportId && log.action === 'download_signed') {
                // Open signed PDF in new tab
                const signedUrl = `/api/reports/${reportId}/signed`
                window.open(signedUrl, '_blank')
              }
            }

            return (
              <TableRow
                key={index}
                className={`transition-all duration-300 ${
                  isNewEvent
                    ? 'bg-blue-50 border-l-4 border-blue-400 animate-pulse'
                    : index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'
                } ${
                  isSignatureEvent && log.action === 'download_signed'
                    ? 'hover:bg-blue-50 cursor-pointer'
                    : 'hover:bg-gray-50'
                }`}
                onClick={handleRowClick}
              >
                <TableCell className="font-mono text-sm">
                  {formatTimestamp(log.timestamp)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <IconComponent className="h-4 w-4 text-gray-500" />
                    <Badge className={`text-xs ${eventColor}`}>
                      {formatEventType(log.action)}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell className="font-mono text-sm">
                  {getResourceId(log.details)}
                </TableCell>
                <TableCell className="text-sm">
                  {log.user}
                </TableCell>
                <TableCell className="text-sm text-gray-600 max-w-xs truncate">
                  {formatDetails(log.details)}
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}

export default AuditTable
