import { useState, useEffect, useCallback } from 'react'
import { Clock, AlertCircle, RefreshCw, Wifi, WifiOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { auditApi, apiUtils } from '@/lib/api'
import { SearchBar, AuditTable, PaginationControls, LoadingSpinner } from '@/components/shared'
import { useAuditWebSocket } from '@/hooks/useAuditWebSocket'
import toast from 'react-hot-toast'

const AuditPage = () => {
  const [logs, setLogs] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [refreshing, setRefreshing] = useState(false)
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [itemsPerPage, setItemsPerPage] = useState(50)
  
  // Filter and sort state
  const [searchTerm, setSearchTerm] = useState('')
  const [sortField, setSortField] = useState('timestamp')
  const [sortOrder, setSortOrder] = useState('desc')

  // Real-time updates state
  const [liveUpdatesEnabled, setLiveUpdatesEnabled] = useState(false)
  const [newEventIds, setNewEventIds] = useState(new Set())

  // Handle new audit events from WebSocket
  const handleNewAuditEvent = useCallback((eventData) => {
    if (!liveUpdatesEnabled) return

    // Transform WebSocket event to match our log format
    const newLog = {
      timestamp: eventData.timestamp,
      action: eventData.event_type,
      user: eventData.user,
      details: eventData.resource_id ? { resource_id: eventData.resource_id } : {}
    }

    // Add to logs if it matches current filters
    setLogs(prevLogs => {
      // Check if event matches search filter
      if (searchTerm) {
        const searchLower = searchTerm.toLowerCase()
        const matchesSearch =
          newLog.action.toLowerCase().includes(searchLower) ||
          newLog.user.toLowerCase().includes(searchLower) ||
          (newLog.details.resource_id && newLog.details.resource_id.toLowerCase().includes(searchLower))

        if (!matchesSearch) return prevLogs
      }

      // Create unique ID for highlighting
      const eventId = `${newLog.timestamp}-${newLog.action}-${newLog.user}`
      setNewEventIds(prev => new Set([...prev, eventId]))

      // Remove highlight after 3 seconds
      setTimeout(() => {
        setNewEventIds(prev => {
          const newSet = new Set(prev)
          newSet.delete(eventId)
          return newSet
        })
      }, 3000)

      // Add to beginning of logs and maintain limit
      const updatedLogs = [newLog, ...prevLogs]
      return updatedLogs.slice(0, itemsPerPage)
    })
  }, [liveUpdatesEnabled, searchTerm, itemsPerPage])

  // WebSocket connection
  const { isConnected, connectionError } = useAuditWebSocket(liveUpdatesEnabled, handleNewAuditEvent)

  const fetchAuditLogs = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) {
        setLoading(true)
      } else {
        setRefreshing(true)
      }
      setError(null)

      const params = {
        page: currentPage,
        limit: itemsPerPage,
        sort: sortOrder,
        ...(searchTerm && { search: searchTerm })
      }

      const response = await auditApi.getLogs(params)
      
      setLogs(response.results || [])
      setTotalPages(response.pages || 1)
      setTotalItems(response.total || 0)
      
    } catch (error) {
      const message = apiUtils.handleError(error, 'Failed to fetch audit logs')
      setError(message)
      setLogs([])
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }, [currentPage, itemsPerPage, sortOrder, searchTerm])

  // Initial load
  useEffect(() => {
    fetchAuditLogs()
  }, [fetchAuditLogs])

  // Reset to first page when search or sort changes
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1)
    }
  }, [searchTerm, sortOrder, itemsPerPage])

  const handleSearch = (term) => {
    setSearchTerm(term)
  }

  const handleSort = (field, order) => {
    setSortField(field)
    setSortOrder(order)
  }

  const handlePageChange = (page) => {
    setCurrentPage(page)
  }

  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(parseInt(value))
    setCurrentPage(1)
  }

  const handleRefresh = () => {
    fetchAuditLogs(false)
    toast.success('Audit logs refreshed')
  }

  const handleRetry = () => {
    fetchAuditLogs()
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Audit Logs</h1>
            <p className="text-gray-600">Loading audit events...</p>
          </div>
          <LoadingSpinner />
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold text-gray-900">Audit Logs</h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            View and search through all system audit events including template uploads, 
            report generation, downloads, and user activities.
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error}
              <Button 
                variant="link" 
                size="sm" 
                onClick={handleRetry}
                className="ml-2 text-red-600 hover:text-red-800 p-0 h-auto"
              >
                Try again
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Audit Events</span>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                disabled={refreshing}
                className="flex items-center space-x-2"
              >
                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </Button>
            </CardTitle>
            <CardDescription>
              {totalItems > 0 ? (
                `${totalItems} audit event${totalItems !== 1 ? 's' : ''} found`
              ) : (
                'No audit events found'
              )}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Search and filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <SearchBar
                  placeholder="Search by event type or resource ID..."
                  onSearch={handleSearch}
                  initialValue={searchTerm}
                  className="w-full"
                />
              </div>
              <div className="flex items-center space-x-4">
                {/* Live Updates Toggle */}
                <div className="flex items-center space-x-2">
                  <Label htmlFor="live-updates" className="text-sm text-gray-600 whitespace-nowrap">
                    Live Updates
                  </Label>
                  <Switch
                    id="live-updates"
                    checked={liveUpdatesEnabled}
                    onCheckedChange={setLiveUpdatesEnabled}
                  />
                  {isConnected ? (
                    <Wifi className="h-4 w-4 text-green-600" title="Connected" />
                  ) : liveUpdatesEnabled ? (
                    <WifiOff className="h-4 w-4 text-red-600" title="Disconnected" />
                  ) : (
                    <WifiOff className="h-4 w-4 text-gray-400" title="Disabled" />
                  )}
                </div>

                {/* Items per page */}
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600 whitespace-nowrap">Show:</span>
                  <Select value={itemsPerPage.toString()} onValueChange={handleItemsPerPageChange}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Connection Status */}
            {connectionError && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <WifiOff className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-800">
                  WebSocket connection error: {connectionError}
                </AlertDescription>
              </Alert>
            )}

            {/* Audit Table */}
            <AuditTable
              logs={logs}
              loading={refreshing}
              onSort={handleSort}
              sortField={sortField}
              sortOrder={sortOrder}
              newEventIds={newEventIds}
            />

            {/* Pagination */}
            {totalPages > 1 && (
              <PaginationControls
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onPageChange={handlePageChange}
                className="mt-6"
              />
            )}
          </CardContent>
        </Card>

        {/* Summary Stats */}
        {logs.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{totalItems}</div>
                  <div className="text-sm text-gray-600">Total Events</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {logs.filter(log => log.action.includes('generated')).length}
                  </div>
                  <div className="text-sm text-gray-600">Generated</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {logs.filter(log => log.action.includes('downloaded')).length}
                  </div>
                  <div className="text-sm text-gray-600">Downloaded</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">
                    {logs.filter(log => log.action.includes('uploaded')).length}
                  </div>
                  <div className="text-sm text-gray-600">Uploaded</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {logs.filter(log => log.action === 'sign' || log.action === 'report_signed').length}
                  </div>
                  <div className="text-sm text-gray-600">Signed</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

export default AuditPage
