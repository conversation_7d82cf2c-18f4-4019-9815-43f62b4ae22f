import { useState, useEffect } from 'react'
import { Plus, FileText, Eye, Trash2, Upload, Calendar, Hash } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { templatesApi, apiUtils } from '@/lib/api'
import { LoadingSpinner, Modal, ConfirmModal, InfoModal, FileUploader } from '@/components/shared'
import { useTemplateContext } from '@/contexts/TemplateContext'
import toast from 'react-hot-toast'

const TemplatesPage = () => {
  const { triggerTemplateRefresh } = useTemplateContext()
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Modal states
  const [showUploadModal, setShowUploadModal] = useState(false)
  const [showPlaceholdersModal, setShowPlaceholdersModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    fetchTemplates()
  }, [])

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await templatesApi.getAll()
      setTemplates(data)
    } catch (error) {
      const message = apiUtils.handleError(error, 'Failed to fetch templates')
      setError(message)
    } finally {
      setLoading(false)
    }
  }

  const handleUploadTemplate = async (file) => {
    try {
      setUploading(true)
      const result = await templatesApi.upload(file)

      // Refresh templates list first
      await fetchTemplates()

      // Trigger refresh in other components (like GeneratePage)
      triggerTemplateRefresh()

      // Close modal and show success only after refresh is complete
      setShowUploadModal(false)
      toast.success('Template uploaded successfully!')

      return result
    } catch (error) {
      apiUtils.handleError(error, 'Failed to upload template')
      throw error
    } finally {
      setUploading(false)
    }
  }

  const handleViewPlaceholders = async (template) => {
    try {
      const data = await templatesApi.getPlaceholders(template.id)
      setSelectedTemplate(data)
      setShowPlaceholdersModal(true)
    } catch (error) {
      apiUtils.handleError(error, 'Failed to fetch template placeholders')
    }
  }

  const handleDeleteTemplate = async () => {
    if (!selectedTemplate) return

    try {
      await templatesApi.delete(selectedTemplate.id)
      toast.success('Template deleted successfully!')
      setShowDeleteModal(false)
      setSelectedTemplate(null)
      
      // Refresh templates list
      await fetchTemplates()
    } catch (error) {
      apiUtils.handleError(error, 'Failed to delete template')
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (loading) {
    return <LoadingSpinner size="lg" text="Loading templates..." />
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Template Management</h1>
          <p className="text-gray-600 mt-1">
            Manage your document templates and view their placeholders
          </p>
        </div>
        <Button onClick={() => setShowUploadModal(true)} className="flex items-center justify-center space-x-2 w-full sm:w-auto">
          <Plus className="h-4 w-4" />
          <span>Upload Template</span>
        </Button>
      </div>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-red-600 text-center">
              <p className="mb-4">{error}</p>
              <Button onClick={fetchTemplates} variant="outline">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Templates Table */}
      {!error && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Available Templates</span>
            </CardTitle>
            <CardDescription>
              {templates.length} template{templates.length !== 1 ? 's' : ''} available
            </CardDescription>
          </CardHeader>
          <CardContent>
            {templates.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Templates Found</h3>
                <p className="text-gray-600 mb-6">
                  Get started by uploading your first template
                </p>
                <Button onClick={() => setShowUploadModal(true)}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Template
                </Button>
              </div>
            ) : (
              <div className="overflow-x-auto -mx-6 sm:mx-0">
                <div className="min-w-full px-6 sm:px-0">
                  <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Template Name</TableHead>
                      <TableHead className="text-center">Placeholders</TableHead>
                      <TableHead>Created At</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {templates.map((template) => (
                      <TableRow key={template.id}>
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="p-2 bg-blue-100 rounded-lg">
                              <FileText className="h-4 w-4 text-blue-600" />
                            </div>
                            <div>
                              <p className="font-medium text-gray-900">{template.name}</p>
                              <p className="text-sm text-gray-500">ID: {template.id}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          <Badge variant="secondary" className="flex items-center justify-center w-fit mx-auto">
                            <Hash className="h-3 w-3 mr-1" />
                            {template.placeholder_count || 'N/A'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Calendar className="h-4 w-4" />
                            <span>{formatDate(template.created_at)}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewPlaceholders(template)}
                              className="flex items-center space-x-1"
                            >
                              <Eye className="h-4 w-4" />
                              <span>View</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setSelectedTemplate(template)
                                setShowDeleteModal(true)
                              }}
                              className="flex items-center space-x-1 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                              <span>Delete</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Upload Modal */}
      <Modal
        isOpen={showUploadModal}
        onClose={() => !uploading && setShowUploadModal(false)}
        title="Upload New Template"
        description="Upload a DOCX file to create a new template"
        size="lg"
        closeOnOverlayClick={!uploading}
        closeOnEscape={!uploading}
      >
        <FileUploader
          onUploadComplete={handleUploadTemplate}
          acceptedTypes={['.docx']}
          maxSizeInMB={5}
          disabled={uploading}
        />
      </Modal>

      {/* Placeholders Modal */}
      <InfoModal
        isOpen={showPlaceholdersModal}
        onClose={() => setShowPlaceholdersModal(false)}
        title={selectedTemplate ? `Placeholders - ${selectedTemplate.name}` : 'Template Placeholders'}
        size="lg"
        content={
          selectedTemplate && (
            <div className="space-y-4">
              <p className="text-gray-600">
                This template contains {selectedTemplate.placeholders?.length || 0} placeholders:
              </p>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {selectedTemplate.placeholders?.map((placeholder, index) => (
                  <Badge key={index} variant="outline" className="justify-center">
                    {placeholder}
                  </Badge>
                )) || <p className="text-gray-500">No placeholders found</p>}
              </div>
            </div>
          )
        }
      />

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteTemplate}
        title="Delete Template"
        message={`Are you sure you want to delete "${selectedTemplate?.name}"? This action cannot be undone.`}
        confirmText="Delete"
        variant="destructive"
      />
    </div>
  )
}

export default TemplatesPage
