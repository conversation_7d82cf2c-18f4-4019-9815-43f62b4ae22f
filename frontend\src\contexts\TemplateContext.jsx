import { createContext, useContext, useState, useCallback } from 'react'

const TemplateContext = createContext()

export const useTemplateContext = () => {
  const context = useContext(TemplateContext)
  if (!context) {
    throw new Error('useTemplateContext must be used within a TemplateProvider')
  }
  return context
}

export const TemplateProvider = ({ children }) => {
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const triggerTemplateRefresh = useCallback(() => {
    setRefreshTrigger(prev => prev + 1)
  }, [])

  const value = {
    refreshTrigger,
    triggerTemplateRefresh
  }

  return (
    <TemplateContext.Provider value={value}>
      {children}
    </TemplateContext.Provider>
  )
}
