import { useState, useEffect } from 'react'
import { ArrowLeft, Send, Download, FileText, CheckCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { templatesApi, reportsApi, apiUtils } from '@/lib/api'
import { LoadingSpinner, InlineSpinner } from '@/components/shared'
import { useTemplateContext } from '@/contexts/TemplateContext'
import { useReportProgress } from '@/hooks/useReportProgress'
import { Progress } from '@/components/ui/progress'
import toast from 'react-hot-toast'

const GeneratePage = () => {
  const { refreshTrigger } = useTemplateContext()
  const [templates, setTemplates] = useState([])
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [templateDetails, setTemplateDetails] = useState(null)
  const [formData, setFormData] = useState({})
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState(false)
  const [generatedReport, setGeneratedReport] = useState(null)
  const [error, setError] = useState(null)
  const [progressData, setProgressData] = useState(null)
  const [currentReportId, setCurrentReportId] = useState(null)

  useEffect(() => {
    fetchTemplates()
  }, [])

  // Refresh templates when triggered from other components
  useEffect(() => {
    if (refreshTrigger > 0) {
      fetchTemplates()
    }
  }, [refreshTrigger])

  // WebSocket handlers for real-time progress
  const handleProgress = (data) => {
    if (data.report_id === currentReportId) {
      setProgressData(data)
    }
  }

  const handleComplete = (data) => {
    if (data.report_id === currentReportId) {
      setProgressData(data)
      setGeneratedReport({
        report_id: data.report_id,
        download_urls: {
          pdf: data.pdf_url,
          docx: data.docx_url
        }
      })
      setGenerating(false)
      setCurrentReportId(null)
    }
  }

  const handleError = (data) => {
    if (data.report_id === currentReportId) {
      setError(data.message)
      setGenerating(false)
      setProgressData(null)
      setCurrentReportId(null)
    }
  }

  // Initialize WebSocket connection for progress tracking
  const { isConnected } = useReportProgress(
    generating, // Only connect when generating
    handleProgress,
    handleComplete,
    handleError
  )

  const fetchTemplates = async () => {
    try {
      setLoading(true)
      const data = await templatesApi.getAll()
      setTemplates(data)
    } catch (error) {
      apiUtils.handleError(error, 'Failed to fetch templates')
    } finally {
      setLoading(false)
    }
  }

  const handleTemplateSelect = async (templateId) => {
    if (!templateId) {
      setSelectedTemplate(null)
      setTemplateDetails(null)
      setFormData({})
      return
    }

    try {
      const template = templates.find(t => t.id === templateId)
      setSelectedTemplate(template)
      
      // Fetch template details with placeholders
      const details = await templatesApi.getPlaceholders(templateId)
      setTemplateDetails(details)
      
      // Reset form data
      setFormData({})
      setError(null)
    } catch (error) {
      apiUtils.handleError(error, 'Failed to fetch template details')
    }
  }

  const handleInputChange = (placeholder, value) => {
    setFormData(prev => ({
      ...prev,
      [placeholder]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    if (!selectedTemplate || !templateDetails) return

    setGenerating(true)
    setError(null)
    setProgressData(null)
    setGeneratedReport(null)

    try {
      // Start generation and get report ID
      const result = await reportsApi.generate(selectedTemplate.id, formData)

      // Set the current report ID for progress tracking
      setCurrentReportId(result.report_id)

      // If WebSocket is not connected or generation completes very quickly,
      // fall back to the traditional approach
      if (!isConnected || result.download_urls) {
        setGeneratedReport(result)
        setGenerating(false)
        toast.success('Report generated successfully!')
      }
      // Otherwise, the WebSocket handlers will manage the completion

    } catch (error) {
      const message = apiUtils.handleError(error, 'Failed to generate report')
      setError(message)
      setGenerating(false)
      setProgressData(null)
      setCurrentReportId(null)
    }
  }

  const handleDownload = async (format) => {
    if (!generatedReport) return

    try {
      const blob = await reportsApi.download(generatedReport.report_id, format)
      apiUtils.downloadBlob(blob, `report-${generatedReport.report_id}.${format}`)
      toast.success(`${format.toUpperCase()} downloaded successfully!`)
    } catch (error) {
      apiUtils.handleError(error, `Failed to download ${format.toUpperCase()}`)
    }
  }

  const handleStartOver = () => {
    setGeneratedReport(null)
    setSelectedTemplate(null)
    setTemplateDetails(null)
    setFormData({})
    setError(null)
    setProgressData(null)
    setCurrentReportId(null)
    setGenerating(false)
  }

  const getInputType = (placeholder) => {
    const lowerPlaceholder = placeholder.toLowerCase()
    if (lowerPlaceholder.includes('date')) return 'date'
    if (lowerPlaceholder.includes('time')) return 'time'
    if (lowerPlaceholder.includes('email')) return 'email'
    if (lowerPlaceholder.includes('phone') || lowerPlaceholder.includes('mobile')) return 'tel'
    if (lowerPlaceholder.includes('number') || lowerPlaceholder.includes('kms') || lowerPlaceholder.includes('rate')) return 'number'
    return 'text'
  }

  const isTextArea = (placeholder) => {
    const lowerPlaceholder = placeholder.toLowerCase()
    return lowerPlaceholder.includes('history') || 
           lowerPlaceholder.includes('investigation') || 
           lowerPlaceholder.includes('conclusion') || 
           lowerPlaceholder.includes('responsibility') ||
           lowerPlaceholder.includes('object')
  }

  const formatPlaceholderLabel = (placeholder) => {
    return placeholder
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim()
  }

  if (loading) {
    return <LoadingSpinner size="lg" text="Loading templates..." />
  }

  // Success state - show generated report
  if (generatedReport) {
    return (
      <div className="max-w-2xl mx-auto space-y-6">
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <div className="flex items-center space-x-3">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div>
                <CardTitle className="text-green-800">Report Generated Successfully!</CardTitle>
                <CardDescription className="text-green-700">
                  Your report has been generated and is ready for download.
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Button 
                onClick={() => handleDownload('docx')}
                className="flex items-center justify-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>Download DOCX</span>
              </Button>
              <Button 
                onClick={() => handleDownload('pdf')}
                variant="outline"
                className="flex items-center justify-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>Download PDF</span>
              </Button>
            </div>
            
            <div className="pt-4 border-t border-green-200">
              <Button 
                onClick={handleStartOver}
                variant="outline"
                className="w-full"
              >
                Generate Another Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-4 sm:space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Generate Report</h1>
        <p className="text-gray-600 mt-1">
          Select a template and fill in the required information to generate your report
        </p>
      </div>

      {/* Template Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Template</CardTitle>
          <CardDescription>
            Choose the template you want to use for your report
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Select value={selectedTemplate?.id || ''} onValueChange={handleTemplateSelect}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a template..." />
            </SelectTrigger>
            <SelectContent>
              {templates.map((template) => (
                <SelectItem key={template.id} value={template.id}>
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4" />
                    <span>{template.name}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Form */}
      {templateDetails && (
        <Card>
          <CardHeader>
            <CardTitle>Report Information</CardTitle>
            <CardDescription>
              Fill in all the required information for your {templateDetails.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                {templateDetails.placeholders.map((placeholder) => (
                  <div key={placeholder} className="space-y-2">
                    <Label htmlFor={placeholder}>
                      {formatPlaceholderLabel(placeholder)}
                    </Label>
                    {isTextArea(placeholder) ? (
                      <Textarea
                        id={placeholder}
                        value={formData[placeholder] || ''}
                        onChange={(e) => handleInputChange(placeholder, e.target.value)}
                        placeholder={`Enter ${formatPlaceholderLabel(placeholder).toLowerCase()}`}
                        rows={3}
                        disabled={generating}
                      />
                    ) : (
                      <Input
                        id={placeholder}
                        type={getInputType(placeholder)}
                        value={formData[placeholder] || ''}
                        onChange={(e) => handleInputChange(placeholder, e.target.value)}
                        placeholder={`Enter ${formatPlaceholderLabel(placeholder).toLowerCase()}`}
                        disabled={generating}
                      />
                    )}
                  </div>
                ))}
              </div>

              {/* Progress indicator */}
              {generating && progressData && (
                <div className="space-y-4 pt-6 border-t">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium text-gray-700">
                        {progressData.message || 'Generating report...'}
                      </span>
                      <span className="text-sm text-gray-500">
                        {progressData.progress || 0}%
                      </span>
                    </div>
                    <Progress
                      value={progressData.progress || 0}
                      className="w-full h-2"
                    />
                  </div>
                  {progressData.step && (
                    <div className="text-xs text-gray-500 capitalize">
                      Current step: {progressData.step.replace('_', ' ')}
                    </div>
                  )}
                </div>
              )}

              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 pt-6 border-t">
                <Button 
                  type="submit" 
                  disabled={generating}
                  className="flex items-center space-x-2"
                >
                  {generating ? (
                    <InlineSpinner />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                  <span>
                    {generating
                      ? (progressData?.message || 'Generating...')
                      : 'Generate Report'
                    }
                  </span>
                </Button>
                
                <Button 
                  type="button" 
                  onClick={() => setSelectedTemplate(null)}
                  variant="outline"
                  disabled={generating}
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Templates
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Empty state when no template selected */}
      {!templateDetails && templates.length > 0 && (
        <Card className="border-dashed">
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Template</h3>
              <p className="text-gray-600">
                Choose a template from the dropdown above to start generating your report
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default GeneratePage
