import { useState, useEffect } from 'react'
import { Search, Download, Eye, Filter, Calendar, FileText, PenTool, CheckCircle } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { reportsApi, templatesApi, apiUtils } from '@/lib/api'
import { LoadingSpinner, Modal, PDFPreview } from '@/components/shared'
import SignatureUploadModal from '@/components/SignatureUploadModal'
import { useReportProgress } from '@/hooks/useReportProgress'
import toast from 'react-hot-toast'

const ReportsPage = () => {
  const [reports, setReports] = useState([])
  const [filteredReports, setFilteredReports] = useState([])
  const [templates, setTemplates] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  
  // Filter states
  const [searchQuery, setSearchQuery] = useState('')
  const [templateFilter, setTemplateFilter] = useState('all')
  
  // Modal states
  const [showPreviewModal, setShowPreviewModal] = useState(false)
  const [showSignatureModal, setShowSignatureModal] = useState(false)
  const [selectedReport, setSelectedReport] = useState(null)
  const [reportToSign, setReportToSign] = useState(null)

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    filterReports()
  }, [reports, searchQuery, templateFilter])

  // WebSocket handlers for real-time signature updates
  const handleSignatureEvent = (data) => {
    console.log('Signature event received:', data)

    // Update the reports list to reflect signature changes
    setReports(prevReports =>
      prevReports.map(report =>
        report.id === data.report_id
          ? { ...report, e_signed: data.event === 'report_signed' }
          : report
      )
    )
  }

  // Initialize WebSocket connection for signature events
  const { isConnected } = useReportProgress(
    true, // Always enabled for signature events
    null, // No progress handler needed
    null, // No completion handler needed
    null, // No error handler needed
    handleSignatureEvent // Signature event handler
  )

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      // Fetch both reports and templates
      const [reportsData, templatesData] = await Promise.all([
        reportsApi.getAll({ limit: 50 }),
        templatesApi.getAll()
      ])
      
      setReports(reportsData)
      setTemplates(templatesData)
    } catch (error) {
      const message = apiUtils.handleError(error, 'Failed to fetch reports')
      setError(message)
    } finally {
      setLoading(false)
    }
  }

  const filterReports = () => {
    let filtered = reports

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(report =>
        report.template_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        report.id?.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Apply template filter
    if (templateFilter !== 'all') {
      filtered = filtered.filter(report => report.template_id === templateFilter)
    }

    setFilteredReports(filtered)
  }

  const handlePreview = (report) => {
    setSelectedReport(report)
    setShowPreviewModal(true)
  }

  const handleSignReport = (report) => {
    setReportToSign(report)
    setShowSignatureModal(true)
  }

  const handleSignatureUploaded = (result) => {
    console.log('Signature uploaded:', result)

    // Update the specific report in the list
    setReports(prevReports =>
      prevReports.map(report =>
        report.id === result.report_id
          ? { ...report, e_signed: true }
          : report
      )
    )

    // Close modal
    setShowSignatureModal(false)
    setReportToSign(null)

    toast.success('Signature uploaded successfully!')
  }

  const handleDownloadSigned = async (report) => {
    try {
      const blob = await reportsApi.downloadSigned(report.id)
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${report.template_name || 'Report'}-${report.id}-signed.pdf`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      const message = apiUtils.handleError(error, 'Failed to download signed PDF')
      toast.error(message)
    }
  }

  const handleDownload = async (report, format) => {
    try {
      const blob = await reportsApi.download(report.id, format)
      apiUtils.downloadBlob(blob, `report-${report.id}.${format}`)
      toast.success(`${format.toUpperCase()} downloaded successfully!`)
    } catch (error) {
      apiUtils.handleError(error, `Failed to download ${format.toUpperCase()}`)
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTemplateName = (templateId) => {
    const template = templates.find(t => t.id === templateId)
    return template?.name || 'Unknown Template'
  }

  if (loading) {
    return <LoadingSpinner size="lg" text="Loading reports..." />
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Generated Reports</h1>
        <p className="text-gray-600 mt-1">
          View, preview, and download your generated reports
        </p>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Search & Filter</span>
          </CardTitle>
          <CardDescription>
            Find specific reports using search and filters
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search reports by name or ID..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="md:w-64">
              <Select value={templateFilter} onValueChange={setTemplateFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by template" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Templates</SelectItem>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Error State */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-red-600 text-center">
              <p className="mb-4">{error}</p>
              <Button onClick={fetchData} variant="outline">
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reports Table */}
      {!error && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5" />
              <span>Reports</span>
            </CardTitle>
            <CardDescription>
              Showing {filteredReports.length} of {reports.length} reports
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredReports.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {reports.length === 0 ? 'No Reports Generated' : 'No Reports Found'}
                </h3>
                <p className="text-gray-600">
                  {reports.length === 0 
                    ? 'Start by generating your first report from a template.'
                    : 'Try adjusting your search or filter criteria.'
                  }
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto -mx-6 sm:mx-0">
                <div className="min-w-full px-6 sm:px-0">
                  <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Report ID</TableHead>
                      <TableHead>Template Name</TableHead>
                      <TableHead>Created At</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredReports.map((report) => (
                      <TableRow key={report.id}>
                        <TableCell>
                          <div className="font-mono text-sm">
                            {report.id.substring(0, 8)}...
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-blue-600" />
                            <span className="font-medium">
                              {report.template_name || getTemplateName(report.template_id)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <Calendar className="h-4 w-4" />
                            <span>{formatDate(report.created_at)}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {report.e_signed ? (
                              <Badge variant="default" className="text-xs bg-green-600 animate-fade-in">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Signed
                              </Badge>
                            ) : (
                              <Badge variant="outline" className="text-xs">
                                <FileText className="h-3 w-3 mr-1" />
                                Generated
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end space-x-1 sm:space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handlePreview(report)}
                              className="flex items-center space-x-1"
                            >
                              <Eye className="h-4 w-4" />
                              <span className="hidden sm:inline">Preview</span>
                            </Button>

                            {report.e_signed ? (
                              // Show signed PDF download button with fade-in animation
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDownloadSigned(report)}
                                className="flex items-center space-x-1 text-green-600 hover:text-green-700 animate-fade-in"
                              >
                                <Download className="h-4 w-4" />
                                <span className="hidden sm:inline">Signed PDF</span>
                              </Button>
                            ) : (
                              // Show regular download buttons and sign button
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownload(report, 'pdf')}
                                  className="flex items-center space-x-1"
                                >
                                  <Download className="h-4 w-4" />
                                  <span className="hidden sm:inline">PDF</span>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDownload(report, 'docx')}
                                  className="flex items-center space-x-1"
                                >
                                  <Download className="h-4 w-4" />
                                  <span className="hidden sm:inline">DOCX</span>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleSignReport(report)}
                                  className="flex items-center space-x-1 text-blue-600 hover:text-blue-700"
                                >
                                  <PenTool className="h-4 w-4" />
                                  <span className="hidden sm:inline">Sign</span>
                                </Button>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                  </Table>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Preview Modal */}
      <Modal
        isOpen={showPreviewModal}
        onClose={() => setShowPreviewModal(false)}
        title={selectedReport ? `Preview - ${selectedReport.template_name || 'Report'}` : 'Report Preview'}
        size="full"
      >
        {selectedReport && (
          <PDFPreview
            reportId={selectedReport.id}
            title={selectedReport.template_name || 'Report'}
            height="70vh"
            showDownloadButton={true}
            showExternalButton={true}
          />
        )}
      </Modal>

      {/* Signature Upload Modal */}
      {showSignatureModal && reportToSign && (
        <SignatureUploadModal
          report={reportToSign}
          onClose={() => {
            setShowSignatureModal(false)
            setReportToSign(null)
          }}
          onSignatureUploaded={handleSignatureUploaded}
          isOpen={showSignatureModal}
        />
      )}
    </div>
  )
}

export default ReportsPage
