import { useState, useEffect, useRef, useCallback } from 'react'
import { io } from 'socket.io-client'
import toast from 'react-hot-toast'

const WEBSOCKET_URL = 'http://localhost:6000'
const NAMESPACE = '/ws/reports'
const RECONNECT_ATTEMPTS = 3
const RECONNECT_DELAY = 2000

/**
 * Custom hook for managing WebSocket connection to report generation progress
 * 
 * @param {boolean} enabled - Whether to enable the WebSocket connection
 * @param {function} onProgress - Callback function when progress update is received
 * @param {function} onComplete - Callback function when report generation is complete
 * @param {function} onError - Callback function when an error occurs
 * @returns {object} WebSocket connection state and controls
 */
export const useReportProgress = (enabled = false, onProgress = null, onComplete = null, onError = null) => {
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState(null)
  const [reconnectAttempts, setReconnectAttempts] = useState(0)
  const [currentProgress, setCurrentProgress] = useState(null)
  const socketRef = useRef(null)
  const reconnectTimeoutRef = useRef(null)

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      return // Already connected
    }

    try {
      console.log('Connecting to report progress WebSocket...')
      
      const socket = io(WEBSOCKET_URL + NAMESPACE, {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true,
        reconnection: true,
        reconnectionAttempts: RECONNECT_ATTEMPTS,
        reconnectionDelay: RECONNECT_DELAY,
        auth: {
          user_id: 'anonymous' // In a real app, use actual user ID from auth
        }
      })

      // Connection successful
      socket.on('connect', () => {
        console.log('Connected to report progress WebSocket')
        setIsConnected(true)
        setConnectionError(null)
        setReconnectAttempts(0)
      })

      // Connection failed
      socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error)
        setIsConnected(false)
        setConnectionError(error.message || 'Connection failed')
        
        // Attempt reconnection
        if (reconnectAttempts < RECONNECT_ATTEMPTS) {
          setReconnectAttempts(prev => prev + 1)
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Reconnection attempt ${reconnectAttempts + 1}/${RECONNECT_ATTEMPTS}`)
            connect()
          }, RECONNECT_DELAY)
        }
      })

      // Disconnection
      socket.on('disconnect', (reason) => {
        console.log('Disconnected from report progress WebSocket:', reason)
        setIsConnected(false)
        setCurrentProgress(null)
      })

      // Connection status updates
      socket.on('connection_status', (data) => {
        console.log('Report WebSocket connection status:', data)
      })

      // Report progress updates
      socket.on('report_progress', (data) => {
        console.log('Report progress update:', data)
        setCurrentProgress(data)
        if (onProgress) {
          onProgress(data)
        }
      })

      // Report completion
      socket.on('report_complete', (data) => {
        console.log('Report generation complete:', data)
        setCurrentProgress(data)
        if (onComplete) {
          onComplete(data)
        }
        toast.success('Report generated successfully!')
      })

      // Report error
      socket.on('report_error', (data) => {
        console.error('Report generation error:', data)
        setCurrentProgress(null)
        if (onError) {
          onError(data)
        }
        toast.error(`Report generation failed: ${data.message}`)
      })

      // Health check
      socket.on('pong', (data) => {
        console.log('WebSocket pong received:', data)
      })

      // Generic error handling
      socket.on('error', (error) => {
        console.error('WebSocket error:', error)
        setConnectionError(error.message || 'Unknown error')
        if (onError) {
          onError({ message: error.message || 'Unknown error' })
        }
      })

      socketRef.current = socket

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error)
      setConnectionError(error.message)
    }
  }, [reconnectAttempts, onProgress, onComplete, onError])

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (socketRef.current) {
      console.log('Disconnecting from report progress WebSocket...')
      socketRef.current.disconnect()
      socketRef.current = null
    }

    setIsConnected(false)
    setConnectionError(null)
    setReconnectAttempts(0)
    setCurrentProgress(null)
  }, [])

  // Send ping to check connection health
  const ping = useCallback(() => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('ping')
    }
  }, [])

  // Effect to manage connection based on enabled state
  useEffect(() => {
    if (enabled) {
      connect()
    } else {
      disconnect()
    }

    // Cleanup on unmount
    return () => {
      disconnect()
    }
  }, [enabled, connect, disconnect])

  // Periodic health check
  useEffect(() => {
    if (isConnected) {
      const interval = setInterval(ping, 30000) // Ping every 30 seconds
      return () => clearInterval(interval)
    }
  }, [isConnected, ping])

  return {
    isConnected,
    connectionError,
    currentProgress,
    reconnectAttempts,
    connect,
    disconnect,
    ping
  }
}

export default useReportProgress
