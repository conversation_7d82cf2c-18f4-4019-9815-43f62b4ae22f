import toast from 'react-hot-toast'

// Base configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

// API Error class for better error handling
class ApiError extends Error {
  constructor(message, status, data = null) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.data = data
  }
}

// Generic API request handler with error handling
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  }

  // Remove Content-Type for FormData requests
  if (options.body instanceof FormData) {
    delete config.headers['Content-Type']
  }

  try {
    const response = await fetch(url, config)
    
    // Handle different response types
    let data
    const contentType = response.headers.get('content-type')
    
    if (contentType && contentType.includes('application/json')) {
      data = await response.json()
    } else if (contentType && contentType.includes('text/')) {
      data = await response.text()
    } else {
      data = await response.blob()
    }

    if (!response.ok) {
      const errorMessage = data?.message || data?.error || `HTTP ${response.status}: ${response.statusText}`
      throw new ApiError(errorMessage, response.status, data)
    }

    return data
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    
    // Network or other errors
    console.error('API Request failed:', error)
    throw new ApiError('Network error. Please check your connection.', 0, null)
  }
}

// Templates API
export const templatesApi = {
  // Get all templates
  async getAll() {
    return apiRequest('/templates')
  },

  // Get template placeholders
  async getPlaceholders(templateId) {
    return apiRequest(`/templates/${templateId}/placeholders`)
  },

  // Upload new template
  async upload(file, onProgress = null) {
    const formData = new FormData()
    formData.append('template', file)

    return apiRequest('/templates/upload', {
      method: 'POST',
      body: formData,
    })
  },

  // Delete template (if endpoint exists)
  async delete(templateId) {
    return apiRequest(`/templates/${templateId}`, {
      method: 'DELETE',
    })
  },

  // Get template content
  async getContent(templateId) {
    return apiRequest(`/templates/${templateId}/content`)
  }
}

// Reports API
export const reportsApi = {
  // Get all reports with optional filters
  async getAll(params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/reports?${queryString}` : '/reports'
    return apiRequest(endpoint)
  },

  // Get single report
  async getById(reportId) {
    return apiRequest(`/reports/${reportId}`)
  },

  // Generate report from template
  async generate(templateId, formData) {
    return apiRequest('/reports/generate', {
      method: 'POST',
      body: JSON.stringify({
        template_id: templateId,
        form_data: formData
      })
    })
  },

  // Generate report from uploaded template
  async generateFromUploadedTemplate(templateId, data) {
    return apiRequest('/api/reports/generate', {
      method: 'POST',
      body: JSON.stringify({
        template_id: templateId,
        data: data
      })
    })
  },

  // Download report
  async download(reportId, format = 'pdf') {
    const response = await fetch(`${API_BASE_URL}/reports/${reportId}/download?format=${format}`)
    
    if (!response.ok) {
      throw new ApiError(`Failed to download ${format.toUpperCase()}`, response.status)
    }
    
    return response.blob()
  },

  // Get download URL
  getDownloadUrl(reportId, format = 'pdf') {
    return `${API_BASE_URL}/reports/${reportId}/download?format=${format}`
  },

  // Preview report (for PDF preview)
  getPreviewUrl(reportId) {
    return `${API_BASE_URL}/reports/${reportId}/download?format=pdf`
  },

  // Upload signature for a report
  async uploadSignature(reportId, signatureFile, options = {}) {
    const formData = new FormData()
    formData.append('signature', signatureFile)

    // Add optional parameters
    if (options.position) {
      formData.append('position', JSON.stringify(options.position))
    }
    if (options.size) {
      formData.append('size', JSON.stringify(options.size))
    }
    if (options.page) {
      formData.append('page', options.page.toString())
    }

    const response = await fetch(`${API_BASE_URL}/reports/${reportId}/signature`, {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new ApiError(errorData.error || 'Failed to upload signature', response.status)
    }

    return response.json()
  },

  // Download signed PDF
  async downloadSigned(reportId) {
    const response = await fetch(`${API_BASE_URL}/reports/${reportId}/signed`)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new ApiError(errorData.error || 'Failed to download signed PDF', response.status)
    }

    return response.blob()
  },

  // Get signed PDF URL
  getSignedUrl(reportId) {
    return `${API_BASE_URL}/reports/${reportId}/signed`
  },

  // Check if signature file is valid
  validateSignatureFile(file) {
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg']
    const maxSize = 1024 * 1024 // 1MB

    if (!allowedTypes.includes(file.type)) {
      throw new Error('Invalid file type. Only PNG, JPG, and JPEG files are allowed.')
    }

    if (file.size > maxSize) {
      throw new Error('File size too large. Maximum size is 1MB.')
    }

    return true
  }
}

// Audit API
export const auditApi = {
  // Get audit logs with pagination, search, and sorting
  async getLogs(params = {}) {
    const queryString = new URLSearchParams(params).toString()
    const endpoint = queryString ? `/audit/logs?${queryString}` : '/audit/logs'
    return apiRequest(endpoint)
  }
}

// Utility functions for common operations
export const apiUtils = {
  // Handle API errors with toast notifications
  handleError(error, customMessage = null) {
    console.error('API Error:', error)
    
    let message = customMessage || 'An error occurred'
    
    if (error instanceof ApiError) {
      message = error.message
    } else if (error.message) {
      message = error.message
    }
    
    toast.error(message)
    return message
  },

  // Show success toast
  showSuccess(message) {
    toast.success(message)
  },

  // Show loading toast
  showLoading(message = 'Loading...') {
    return toast.loading(message)
  },

  // Dismiss toast
  dismissToast(toastId) {
    toast.dismiss(toastId)
  },

  // Download file from blob
  downloadBlob(blob, filename) {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  },

  // Format file size
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  // Validate file type
  validateFileType(file, allowedTypes = ['.docx']) {
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
    return allowedTypes.includes(fileExtension)
  },

  // Validate file size (default 5MB)
  validateFileSize(file, maxSizeInMB = 5) {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024
    return file.size <= maxSizeInBytes
  }
}

// Export default API object
export default {
  templates: templatesApi,
  reports: reportsApi,
  audit: auditApi,
  utils: apiUtils
}
