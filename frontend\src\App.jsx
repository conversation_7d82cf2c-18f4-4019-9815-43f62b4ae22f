import { useState } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { FileText, Download, PenTool, Search } from 'lucide-react'
import { Toaster } from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import './App.css'

// Components
import Header from './components/Header'
import TemplateSelector from './components/TemplateSelector'
import FormBuilder from './components/FormBuilder'
import ReportsList from './components/ReportsList'
import SignatureModal from './components/SignatureModal'
import ErrorBoundary from './components/ErrorBoundary'

// Pages
import { TemplatesPage, GeneratePage, ReportsPage, AuditPage } from './pages'

// Contexts
import { TemplateProvider } from './contexts/TemplateContext'

function App() {
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [showSignatureModal, setShowSignatureModal] = useState(false)
  const [reportToSign, setReportToSign] = useState(null)

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template)
  }

  const handleFormSubmit = () => {
    setSelectedTemplate(null)
  }

  const handleSignReport = (report) => {
    setReportToSign(report)
    setShowSignatureModal(true)
  }

  const handleSignatureComplete = () => {
    setShowSignatureModal(false)
    setReportToSign(null)
  }

  return (
    <ErrorBoundary>
      <TemplateProvider>
        <Router>
          <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
          <Header />
        
        <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
          <Routes>
            {/* Home/Dashboard */}
            <Route path="/" element={
              <div className="space-y-8">
                {/* Hero Section */}
                <div className="text-center space-y-4">
                  <h1 className="text-4xl font-bold text-gray-900">
                    Indian Railways Report Generator
                  </h1>
                  <p className="text-xl text-gray-600 max-w-2xl mx-auto">
                    Create professional railway reports from templates with ease. Generate Joint Reports and TA Forms in DOCX or PDF format.
                  </p>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8">
                  <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                    <FileText className="h-12 w-12 text-blue-600 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Template Management</h3>
                    <p className="text-gray-600">Upload and manage your document templates with placeholder extraction</p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                    <Download className="h-12 w-12 text-green-600 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Multiple Formats</h3>
                    <p className="text-gray-600">Generate and download reports in both DOCX and PDF formats</p>
                  </div>
                  <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
                    <PenTool className="h-12 w-12 text-purple-600 mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Digital Signatures</h3>
                    <p className="text-gray-600">Add digital signatures to your reports for authentication</p>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                  <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-lg text-white">
                    <h3 className="text-xl font-semibold mb-2">Generate New Report</h3>
                    <p className="mb-4 opacity-90">Start creating a new report from available templates</p>
                    <Button
                      variant="secondary"
                      onClick={() => window.location.href = '/generate'}
                      className="bg-white text-blue-600 hover:bg-gray-100"
                    >
                      Get Started
                    </Button>
                  </div>
                  <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-lg text-white">
                    <h3 className="text-xl font-semibold mb-2">Manage Templates</h3>
                    <p className="mb-4 opacity-90">Upload new templates or view existing ones</p>
                    <Button
                      variant="secondary"
                      onClick={() => window.location.href = '/templates'}
                      className="bg-white text-green-600 hover:bg-gray-100"
                    >
                      Manage Templates
                    </Button>
                  </div>
                </div>

                {/* Legacy Template Selector for backward compatibility */}
                {!selectedTemplate ? (
                  <TemplateSelector onTemplateSelect={handleTemplateSelect} />
                ) : (
                  <FormBuilder
                    template={selectedTemplate}
                    onFormSubmit={handleFormSubmit}
                    onCancel={() => setSelectedTemplate(null)}
                  />
                )}
              </div>
            } />

            {/* New dedicated pages */}
            <Route path="/templates" element={<TemplatesPage />} />
            <Route path="/generate" element={<GeneratePage />} />
            <Route path="/reports" element={<ReportsPage />} />
            <Route path="/audit" element={<AuditPage />} />

            {/* Legacy reports route */}
            <Route path="/legacy-reports" element={
              <ReportsList onSignReport={handleSignReport} />
            } />
          </Routes>
        </main>

        {/* Signature Modal */}
        {showSignatureModal && (
          <SignatureModal
            report={reportToSign}
            onClose={() => setShowSignatureModal(false)}
            onSignatureComplete={handleSignatureComplete}
          />
        )}

        {/* Toast Notifications */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#363636',
              color: '#fff',
            },
            success: {
              duration: 3000,
              iconTheme: {
                primary: '#4ade80',
                secondary: '#fff',
              },
            },
            error: {
              duration: 5000,
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
          </div>
        </Router>
      </TemplateProvider>
    </ErrorBoundary>
  )
}

export default App

