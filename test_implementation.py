#!/usr/bin/env python3
"""
Test script to verify the implementation works correctly.
"""

import sys
import os
import json

# Add the backend src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend', 'src'))

def test_template_api():
    """Test the updated template API structure (syntax check only)."""
    print("Testing template API structure...")

    try:
        # Just check if the templates route file can be imported
        from src.routes.templates import templates_bp, get_templates
        print("✓ Templates API structure is valid")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error testing templates API: {e}")
        return False

def test_websocket_imports():
    """Test that our WebSocket service can be imported without errors."""
    print("Testing WebSocket service imports...")
    
    try:
        from src.services.report_websocket import (
            ReportWebSocketService, 
            initialize_report_websocket,
            broadcast_report_progress
        )
        print("✓ Report WebSocket service imports successfully")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error importing WebSocket service: {e}")
        return False

def test_report_routes_imports():
    """Test that the updated report routes can be imported."""
    print("Testing report routes imports...")
    
    try:
        from src.routes.reports import reports_bp
        print("✓ Report routes import successfully")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error importing report routes: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing implementation...")
    print("=" * 50)
    
    tests = [
        test_websocket_imports,
        test_report_routes_imports,
        test_template_api,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Implementation looks good.")
        return True
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
